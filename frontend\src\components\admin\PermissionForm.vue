<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    :disabled="isViewMode"
    class="permission-form"
  >
    <!-- Basic Information Section -->
    <div class="form-section">
      <h3 class="section-title">
        <el-icon><Document /></el-icon>
        基本信息
      </h3>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item label="权限名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入权限名称"
              maxlength="100"
              show-word-limit
              clearable
            >
              <template #prefix>
                <el-icon><Edit /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="权限编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入权限编码，如：USER_MANAGEMENT"
              maxlength="100"
              show-word-limit
              :disabled="isEditMode || formData.isSystem"
              clearable
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
              <template #suffix>
                <el-tooltip
                  content="权限编码只能包含大写字母、数字和下划线，且必须以大写字母开头"
                  placement="top"
                >
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item label="资源类型" prop="resourceType">
            <el-select
              v-model="formData.resourceType"
              placeholder="请选择资源类型"
              style="width: 100%"
              @change="handleResourceTypeChange"
            >
              <el-option
                v-for="(label, value) in resourceTypes"
                :key="value"
                :label="label"
                :value="value"
              >
                <span class="resource-type-option">
                  <el-icon :class="getResourceTypeIcon(value)" />
                  <span>{{ label }}</span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" v-if="showHttpMethod">
          <el-form-item label="HTTP方法" prop="httpMethod">
            <el-select
              v-model="formData.httpMethod"
              placeholder="请选择HTTP方法"
              style="width: 100%"
            >
              <el-option
                v-for="(method, index) in httpMethods"
                :key="index"
                :label="method"
                :value="method"
              >
                <span :class="['http-method', method.toLowerCase()]">
                  {{ method }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item
        label="资源路径"
        prop="resourcePath"
        v-if="showResourcePath"
      >
        <el-input
          v-model="formData.resourcePath"
          placeholder="请输入资源路径，如：/api/users"
          maxlength="255"
          clearable
        >
          <template #prefix>
            <el-icon><Link /></el-icon>
          </template>
        </el-input>
      </el-form-item>
    </div>

    <!-- Hierarchy Section -->
    <div class="form-section">
      <h3 class="section-title">
        <el-icon><Operation /></el-icon>
        层级与排序
      </h3>
      <el-form-item
        label="父权限"
        prop="parentId"
        :class="{ 'is-required': isContextAddingChild }"
      >
        <el-tree-select
          v-model="formData.parentId"
          :data="parentOptions"
          :props="treeProps"
          placeholder="请选择父权限（可选）"
          clearable
          check-strictly
          style="width: 100%"
          node-key="id"
          :disabled="isContextAddingChild"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item label="层级" prop="level">
            <el-input-number
              v-model="formData.level"
              :min="1"
              :max="5"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- Status Section -->
    <div class="form-section">
      <h3 class="section-title">
        <el-icon><Setting /></el-icon>
        属性与状态
      </h3>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio-button value="ACTIVE">
                <el-icon><Check /></el-icon>
                激活
              </el-radio-button>
              <el-radio-button value="INACTIVE">
                <el-icon><Close /></el-icon>
                未激活
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item label="系统权限" prop="isSystem">
            <el-switch
              v-model="formData.isSystem"
              :disabled="!isCreateMode"
              active-text="是"
              inactive-text="否"
            />
            <el-tooltip
              content="系统权限不可删除，创建后不可修改此属性"
              placement="top"
            >
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="权限描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入权限描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </div>

    <!-- Form Actions -->
    <div class="form-actions" v-if="!isViewMode">
      <el-button @click="handleCancel" :disabled="loading">取消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        :disabled="loading"
      >
        {{ submitButtonText }}
      </el-button>
    </div>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import {
  Document, Edit, Key, QuestionFilled, Link,
  Operation, Setting, Check, Close, InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { permissionService } from '@/api/permissionService'

// Props
const props = defineProps({
  permission: {
    type: Object,
    default: () => ({})
  },
  parentOptions: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value) => ['create', 'edit', 'view'].includes(value)
  },
  parentId: {
    type: [Number, String],
    default: null
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel', 'update:permission'])

// Refs
const formRef = ref()
const loading = ref(false)

// Constants
const resourceTypes = {
  MENU: '菜单',
  BUTTON: '按钮',
  API: '接口',
  DATA: '数据'
}

const httpMethods = ['ALL', 'GET', 'POST', 'PUT', 'DELETE', 'PATCH']

// Form data
const formData = reactive({
  name: '',
  code: '',
  description: '',
  resourceType: 'API',
  resourcePath: '',
  httpMethod: 'ALL',
  parentId: null,
  level: 1,
  sortOrder: 0,
  status: 'ACTIVE',
  isSystem: false
})

// Computed
const isCreateMode = computed(() => props.mode === 'create')
const isEditMode = computed(() => props.mode === 'edit')
const isViewMode = computed(() => props.mode === 'view')
const isContextAddingChild = computed(() => isCreateMode.value && props.parentId != null)
const submitButtonText = computed(() => isCreateMode.value ? '创建' : '更新')
const showHttpMethod = computed(() => formData.resourceType === 'API')
const showResourcePath = computed(() => ['API', 'MENU'].includes(formData.resourceType))

// Tree props
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// Form rules
const formRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 2, max: 100, message: '权限编码长度在 2 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[A-Z][A-Z0-9_]*$/,
      message: '权限编码只能包含大写字母、数字和下划线，且必须以大写字母开头',
      trigger: 'blur'
    },
    { validator: validateCode, trigger: 'blur' }
  ],
  resourceType: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请输入层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 5, message: '层级必须在 1 到 5 之间', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序不能小于 0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// Methods
function getResourceTypeIcon(type) {
  const icons = {
    MENU: 'menu',
    BUTTON: 'button',
    API: 'api',
    DATA: 'data'
  }
  return icons[type] || ''
}

async function validateCode(rule, value, callback) {
  if (!value) {
    callback()
    return
  }

  try {
    const exists = await permissionService.checkPermissionExists(value)
    if (exists && isCreateMode.value) {
      callback(new Error('权限编码已存在'))
    } else if (exists && isEditMode.value && value !== props.permission.code) {
      callback(new Error('权限编码已存在'))
    } else {
      callback()
    }
  } catch (error) {
    console.error('验证权限编码失败:', error)
    callback()
  }
}

function handleResourceTypeChange() {
  // Reset HTTP method when resource type is not API
  if (formData.resourceType !== 'API') {
    formData.httpMethod = null
  } else {
    formData.httpMethod = 'ALL'
  }

  // Clear resource path when not needed
  if (!showResourcePath.value) {
    formData.resourcePath = ''
  }
}

async function handleSubmit() {
  if (loading.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

function handleCancel() {
  emit('cancel')
}

function resetForm() {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: '',
    code: '',
    description: '',
    resourceType: 'API',
    resourcePath: '',
    httpMethod: 'ALL',
    parentId: null,
    level: 1,
    sortOrder: 0,
    status: 'ACTIVE',
    isSystem: false
  })
}

async function validateForm() {
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// Watch props
watch(() => props.permission, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, {
      name: newVal.name || '',
      code: newVal.code || '',
      description: newVal.description || '',
      resourceType: newVal.resourceType || 'API',
      resourcePath: newVal.resourcePath || '',
      httpMethod: newVal.httpMethod || 'ALL',
      parentId: newVal.parentId || null,
      level: newVal.level || 1,
      sortOrder: newVal.sortOrder || 0,
      status: newVal.status || 'ACTIVE',
      isSystem: newVal.isSystem || false
    })
  }
}, { immediate: true, deep: true })

watch(() => props.parentId, (newVal) => {
  if (newVal && isCreateMode.value) {
    formData.parentId = newVal
    formData.level = 2 // Set default level for child permissions
  }
}, { immediate: true })

// Expose methods
defineExpose({
  resetForm,
  handleSubmit,
  validateForm
})
</script>

<style scoped>
.permission-form {
  padding: 20px 0;
}

.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 24px;
  padding-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  text-align: right;
  border-top: 1px solid var(--el-border-color-light);
}

.form-actions .el-button {
  margin-left: 12px;
  min-width: 100px;
}

.info-icon {
  margin-left: 8px;
  color: var(--el-color-info);
  cursor: help;
}

.resource-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-type-option .el-icon {
  font-size: 16px;
}

.http-method {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-weight: 600;
}

.http-method.get { color: #67C23A; background: #f0f9eb; }
.http-method.post { color: #409EFF; background: #ecf5ff; }
.http-method.put { color: #E6A23C; background: #fdf6ec; }
.http-method.delete { color: #F56C6C; background: #fef0f0; }
.http-method.patch { color: #909399; background: #f4f4f5; }
.http-method.all { color: #606266; background: #ebeef5; }

/* Resource type icons */
.menu { color: #409EFF; }
.button { color: #67C23A; }
.api { color: #E6A23C; }
.data { color: #909399; }

@media (max-width: 768px) {
  .permission-form {
    padding: 10px;
  }

  .form-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .form-actions {
    text-align: center;
    margin-top: 24px;
    padding-top: 20px;
  }

  .form-actions .el-button {
    margin: 0 6px;
    min-width: 120px;
  }
}
</style>
