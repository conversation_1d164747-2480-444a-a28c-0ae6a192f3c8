package zh.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.entity.User;
import zh.backend.service.PermissionService;
import zh.backend.service.RoleService;
import zh.backend.service.UserService;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.Set;

/**
 * 用户管理控制器
 */
@RestController
@RequestMapping("/api/users")
@RequirePermission("USER_MANAGEMENT")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取用户列表
     */
    @GetMapping
    @RequirePermission("USER_LIST")
    public ResponseEntity<Map<String, Object>> getUsers(
            @RequestParam(defaultValue = "") String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<User> users = userService.findByKeyword(keyword, pageable);

        // 转换为安全的 DTO 格式，避免懒加载问题
        List<Map<String, Object>> userList = users.getContent().stream()
            .map(this::convertUserToSafeMap)
            .collect(Collectors.toList());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", Map.of(
            "content", userList,
            "totalElements", users.getTotalElements(),
            "totalPages", users.getTotalPages(),
            "size", users.getSize(),
            "number", users.getNumber()
        ));

        return ResponseEntity.ok(response);
    }

    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    @RequirePermission("USER_LIST")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return ResponseEntity.ok(user);
    }

    /**
     * 创建用户
     */
    @PostMapping
    @RequirePermission("USER_CREATE")
    public ResponseEntity<User> createUser(@Valid @RequestBody User user) {
        User createdUser = userService.createUser(user);
        return ResponseEntity.ok(createdUser);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @Valid @RequestBody User user) {
        User updatedUser = userService.updateUser(id, user);
        return ResponseEntity.ok(updatedUser);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @RequirePermission("USER_DELETE")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @RequirePermission("USER_DELETE")
    public ResponseEntity<Void> deleteUsers(@RequestBody List<Long> ids) {
        userService.deleteUsers(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/{id}/reset-password")
    @RequirePermission("USER_RESET_PASSWORD")
    public ResponseEntity<Void> resetPassword(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {
        String newPassword = request.get("newPassword");
        userService.resetPassword(id, newPassword);
        return ResponseEntity.ok().build();
    }

    /**
     * 锁定用户
     */
    @PostMapping("/{id}/lock")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> lockUser(@PathVariable Long id) {
        userService.lockUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 解锁用户
     */
    @PostMapping("/{id}/unlock")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> unlockUser(@PathVariable Long id) {
        userService.unlockUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 激活用户
     */
    @PostMapping("/{id}/activate")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> activateUser(@PathVariable Long id) {
        userService.activateUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 停用用户
     */
    @PostMapping("/{id}/deactivate")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> deactivateUser(@PathVariable Long id) {
        userService.deactivateUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新用户状态
     */
    @PatchMapping("/{id}/status")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
        String status = request.get("status");
        User.UserStatus userStatus = User.UserStatus.valueOf(status);
        userService.updateUserStatus(id, userStatus);
        return ResponseEntity.ok().build();
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/{id}/roles")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> assignRoles(
            @PathVariable Long id,
            @RequestBody List<Long> roleIds) {
        roleService.assignRolesToUser(id, roleIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取用户的角色
     */
    @GetMapping("/{id}/roles")
    @RequirePermission("USER_LIST")
    public ResponseEntity<List<Role>> getUserRoles(@PathVariable Long id) {
        List<Role> roles = roleService.getUserRoles(id);
        return ResponseEntity.ok(roles);
    }

    /**
     * 为用户分配直接权限
     */
    @PostMapping("/{id}/permissions/{permissionId}")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> grantPermission(
            @PathVariable Long id,
            @PathVariable Long permissionId) {
        // 这里假设当前操作用户ID为1，实际应该从SecurityContext获取
        permissionService.grantPermissionToUser(id, permissionId, 1L);
        return ResponseEntity.ok().build();
    }

    /**
     * 撤销用户的直接权限
     */
    @DeleteMapping("/{id}/permissions/{permissionId}")
    @RequirePermission("USER_UPDATE")
    public ResponseEntity<Void> revokePermission(
            @PathVariable Long id,
            @PathVariable Long permissionId) {
        permissionService.revokePermissionFromUser(id, permissionId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取用户的所有权限
     */
    @GetMapping("/{id}/permissions")
    @RequirePermission("USER_LIST")
    public ResponseEntity<Set<String>> getUserPermissions(@PathVariable Long id) {
        Set<String> permissions = permissionService.getUserPermissions(id);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 获取用户的直接权限
     */
    @GetMapping("/{id}/direct-permissions")
    @RequirePermission("USER_LIST")
    public ResponseEntity<List<Permission>> getUserDirectPermissions(@PathVariable Long id) {
        List<Permission> permissions = permissionService.getUserDirectPermissions(id);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 获取用户的角色权限
     */
    @GetMapping("/{id}/role-permissions")
    @RequirePermission("USER_LIST")
    public ResponseEntity<List<Permission>> getUserRolePermissions(@PathVariable Long id) {
        List<Permission> permissions = permissionService.getUserRolePermissions(id);
        return ResponseEntity.ok(permissions);
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/exists/username")
    @RequirePermission("USER_LIST")
    public ResponseEntity<Boolean> checkUsernameExists(@RequestParam String username) {
        boolean exists = userService.existsByUsername(username);
        return ResponseEntity.ok(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/exists/email")
    @RequirePermission("USER_LIST")
    public ResponseEntity<Boolean> checkEmailExists(@RequestParam String email) {
        boolean exists = userService.existsByEmail(email);
        return ResponseEntity.ok(exists);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    @RequirePermission("USER_LIST")
    public ResponseEntity<UserService.UserStatistics> getUserStatistics() {
        UserService.UserStatistics statistics = userService.getUserStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 将 User 实体转换为安全的 Map，避免懒加载问题
     */
    private Map<String, Object> convertUserToSafeMap(User user) {
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("id", user.getId());
        userMap.put("username", user.getUsername());
        userMap.put("email", user.getEmail());
        userMap.put("realName", user.getRealName());
        userMap.put("phone", user.getPhone());
        userMap.put("avatarUrl", user.getAvatarUrl());
        userMap.put("status", user.getStatus());
        userMap.put("lastLoginTime", user.getLastLoginTime());
        userMap.put("createdAt", user.getCreatedAt());
        userMap.put("updatedAt", user.getUpdatedAt());

        // 安全地获取角色信息，避免懒加载异常
        try {
            List<Role> userRoles = roleService.getUserRoles(user.getId());
            List<Map<String, Object>> roleList = userRoles.stream()
                .map(role -> {
                    Map<String, Object> roleMap = new HashMap<>();
                    roleMap.put("id", role.getId());
                    roleMap.put("name", role.getName());
                    roleMap.put("code", role.getCode());
                    roleMap.put("description", role.getDescription());
                    return roleMap;
                })
                .collect(Collectors.toList());
            userMap.put("roles", roleList);
        } catch (Exception e) {
            // 如果获取角色失败，设置为空列表
            userMap.put("roles", List.of());
        }

        return userMap;
    }
}
