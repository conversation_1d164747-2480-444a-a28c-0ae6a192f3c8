package zh.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import zh.backend.entity.UserPermission;

import java.time.LocalDateTime;

/**
 * 用户权限数据传输对象
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserPermissionDTO {
    
    private Long id;
    private Long userId;
    private String username;
    private Long permissionId;
    private String permissionName;
    private String permissionCode;
    private UserPermission.PermissionType permissionType;
    private LocalDateTime grantedAt;
    private Long grantedBy;
    private String grantedByName;
    private LocalDateTime expiresAt;
    private UserPermission.UserPermissionStatus status;
    private String source; // "DIRECT" 或 "ROLE"
    private String roleName; // 如果是通过角色获得的权限
    
    // 构造函数
    public UserPermissionDTO() {}
    
    public UserPermissionDTO(UserPermission userPermission) {
        this.id = userPermission.getId();
        this.userId = userPermission.getUser().getId();
        this.username = userPermission.getUser().getUsername();
        this.permissionId = userPermission.getPermission().getId();
        this.permissionName = userPermission.getPermission().getName();
        this.permissionCode = userPermission.getPermission().getCode();
        this.permissionType = userPermission.getPermissionType();
        this.grantedAt = userPermission.getGrantedAt();
        this.grantedBy = userPermission.getGrantedBy();
        this.expiresAt = userPermission.getExpiresAt();
        this.status = userPermission.getStatus();
        this.source = "DIRECT";
    }
    
    // 检查权限是否已过期
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    // 检查权限是否激活
    public boolean isActive() {
        return status == UserPermission.UserPermissionStatus.ACTIVE && !isExpired();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public Long getPermissionId() {
        return permissionId;
    }
    
    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }
    
    public String getPermissionName() {
        return permissionName;
    }
    
    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }
    
    public String getPermissionCode() {
        return permissionCode;
    }
    
    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }
    
    public UserPermission.PermissionType getPermissionType() {
        return permissionType;
    }
    
    public void setPermissionType(UserPermission.PermissionType permissionType) {
        this.permissionType = permissionType;
    }
    
    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }
    
    public void setGrantedAt(LocalDateTime grantedAt) {
        this.grantedAt = grantedAt;
    }
    
    public Long getGrantedBy() {
        return grantedBy;
    }
    
    public void setGrantedBy(Long grantedBy) {
        this.grantedBy = grantedBy;
    }
    
    public String getGrantedByName() {
        return grantedByName;
    }
    
    public void setGrantedByName(String grantedByName) {
        this.grantedByName = grantedByName;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public UserPermission.UserPermissionStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserPermission.UserPermissionStatus status) {
        this.status = status;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getRoleName() {
        return roleName;
    }
    
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}
