import { defineStore } from 'pinia';
import api from '@/api/axios';

const API_URL = '/api/auth'; // 认证API路径

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: JSON.parse(localStorage.getItem('user')) || null,
    error: null,
    loading: false,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token && !!state.user,
    authStatus: (state) => {
      if (state.loading) return 'loading';
      if (state.error) return 'error';
      if (state.token && state.user) return 'success';
      return 'idle';
    },
    getUser: (state) => state.user,
    getToken: (state) => state.token,
    getError: (state) => state.error,
  },
  actions: {
    async login(credentials) {
      this.loading = true;
      this.error = null;
      try {
        const response = await api.post(`${API_URL}/login`, credentials);
        if (response.data && response.data.success && response.data.token) {
          const { token, user: userData } = response.data;
          this.token = token;
          this.user = {
            id: userData.id,
            username: userData.username,
            email: userData.email,
            realName: userData.realName,
            roles: userData.roles || [],
            permissions: userData.permissions || []
          };
          localStorage.setItem('token', token);
          localStorage.setItem('user', JSON.stringify(this.user));
          // API实例会自动处理认证头
        } else {
          this.error = response.data.message || '登录失败，响应数据格式不正确';
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // API实例会自动处理认证头
        }
      } catch (err) {
        this.error = err.response?.data?.message || err.message || '登录时发生错误';
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // API实例会自动处理认证头
      } finally {
        this.loading = false;
      }
    },
    async register(userData) {
      this.loading = true;
      this.error = null;
      try {
        const response = await api.post(`${API_URL}/register`, userData);
        if (response.data && response.data.success) {
          // 注册成功后可以考虑自动登录或提示用户登录
          return true;
        } else {
          this.error = response.data.message || '注册失败';
          return false;
        }
      } catch (err) {
        this.error = err.response?.data?.message || err.message || '注册时发生错误';
        return false;
      } finally {
        this.loading = false;
      }
    },
    logout() {
      this.token = null;
      this.user = null;
      this.error = null;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // API实例会自动处理认证头
      // 可选：通知后端会话已注销
    },
    async fetchUser() {
      if (!this.token) {
        this.error = '没有可用的 token';
        return;
      }
      this.loading = true;
      this.error = null;
      try {
        const response = await api.get(`${API_URL}/me`);
        this.user = response.data;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (err) {
        this.error = err.response?.data?.message || '获取用户信息失败';
        // 如果token无效或过期，则清除本地存储
        if (err.response?.status === 401 || err.response?.status === 403) {
          this.logout();
        }
      } finally {
        this.loading = false;
      }
    },
    // 初始化时检查 token 和用户信息
    initializeAuth() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      if (token && user) {
        this.token = token;
        this.user = JSON.parse(user);
        // API实例会自动处理认证头
        // 可以选择在这里调用 fetchUser 来验证 token 和刷新用户信息
        // this.fetchUser();
      } else {
        this.logout(); //确保状态一致性
      }
    }
  },
});