package zh.backend;

/**
 * <AUTHOR>
 * @file backend
 * @brief [这里简要描述该文件或类的主要功能，一句话概括]
 * @details [可选：如果需要，这里可以添加更详细的描述、实现说明或注意事项]
 * @date 2025/5/30
 */
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

public class GeneratePassword {
    public static void main(String[] args) {
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String rawPassword = "admin123";
        String encodedPassword = passwordEncoder.encode(rawPassword);

        System.out.println("Plaintext password: " + rawPassword);
        System.out.println("BCrypt encoded password: " + encodedPassword);

        // 示例：验证密码 (可选)
        // boolean isMatch = passwordEncoder.matches(rawPassword, encodedPassword);
        // System.out.println("Password matches: " + isMatch);
    }
}
