<template>
  <div class="role-manage-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><UserFilled /></el-icon>
          角色管理
        </h1>
        <p class="page-description">管理系统角色，包括创建、编辑、删除和权限分配</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ roleStats.total }}</div>
                <div class="stat-label">总角色数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon system">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ roleStats.system }}</div>
                <div class="stat-label">系统角色</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon custom">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ roleStats.custom }}</div>
                <div class="stat-label">自定义角色</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ roleStats.totalUsers }}</div>
                <div class="stat-label">关联用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索角色名称或编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            class="search-input"
          />
          <el-select
            v-model="searchForm.isSystem"
            placeholder="角色类型"
            clearable
            @change="handleSearch"
            class="type-select"
          >
            <el-option label="全部类型" value="" />
            <el-option label="系统角色" :value="true" />
            <el-option label="自定义角色" :value="false" />
          </el-select>
          <el-select
            v-model="searchForm.status"
            placeholder="角色状态"
            clearable
            @change="handleSearch"
            class="status-select"
          >
            <el-option label="全部状态" value="" />
            <el-option label="启用" value="ACTIVE" />
            <el-option label="禁用" value="INACTIVE" />
          </el-select>
        </div>

        <!-- 操作按钮 -->
        <div class="action-area">
          <el-dropdown @command="handleCreateCommand" v-can="'ROLE_CREATE'">
            <el-button type="primary" :icon="Plus">
              新增角色
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="template">
                  <el-icon><Star /></el-icon>
                  使用模板创建
                </el-dropdown-item>
                <el-dropdown-item command="custom">
                  <el-icon><Plus /></el-icon>
                  自定义创建
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown
            v-if="selectedRoles.length > 0"
            @command="handleBatchAction"
            class="batch-dropdown"
          >
            <el-button type="warning">
              批量操作 ({{ selectedRoles.length }})
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="delete" v-can="'ROLE_DELETE'">
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button :icon="Refresh" @click="fetchRoles">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 角色表格 -->
    <el-card class="table-card">
      <el-table
        :data="roles"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
        class="role-table"
      >
        <!-- 多选列 -->
        <el-table-column
          type="selection"
          width="55"
          :selectable="isRowSelectable"
        />

        <!-- 角色信息 -->
        <el-table-column label="角色信息" min-width="200">
          <template #default="{ row }">
            <div class="role-info">
              <div class="role-icon">
                <el-icon v-if="row.isSystem" class="system-icon"><Lock /></el-icon>
                <el-icon v-else class="custom-icon"><User /></el-icon>
              </div>
              <div class="role-details">
                <div class="role-name">{{ row.name }}</div>
                <div class="role-code">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 描述 -->
        <el-table-column label="描述" min-width="200">
          <template #default="{ row }">
            <div class="role-description">
              {{ row.description || '暂无描述' }}
            </div>
          </template>
        </el-table-column>

        <!-- 权限数量 -->
        <el-table-column label="权限数量" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.permissionCount || 0 }} 个权限
            </el-tag>
          </template>
        </el-table-column>

        <!-- 用户数量 -->
        <el-table-column label="用户数量" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="success" size="small">
              {{ row.userCount || 0 }} 个用户
            </el-tag>
          </template>
        </el-table-column>

        <!-- 类型 -->
        <el-table-column label="类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.isSystem ? 'danger' : 'primary'"
              size="small"
            >
              {{ row.isSystem ? '系统' : '自定义' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 排序 -->
        <el-table-column label="排序" width="80" align="center">
          <template #default="{ row }">
            <span class="sort-order">{{ row.sortOrder }}</span>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="编辑" placement="top">
                <el-button
                  link
                  type="primary"
                  :icon="Edit"
                  size="small"
                  @click="handleEdit(row)"
                  v-can="'ROLE_UPDATE'"
                />
              </el-tooltip>

              <el-tooltip content="权限管理" placement="top">
                <el-button
                  link
                  type="warning"
                  :icon="Key"
                  size="small"
                  @click="handlePermissions(row)"
                  v-can="'ROLE_ASSIGN_PERMISSIONS'"
                />
              </el-tooltip>

              <el-tooltip content="用户管理" placement="top">
                <el-button
                  link
                  type="info"
                  :icon="User"
                  size="small"
                  @click="handleUsers(row)"
                  v-can="'ROLE_USER_MANAGE'"
                />
              </el-tooltip>

              <el-tooltip content="删除" placement="top">
                <el-button
                  link
                  type="danger"
                  :icon="Delete"
                  size="small"
                  @click="handleDelete(row)"
                  v-can="'ROLE_DELETE'"
                  v-if="!row.isSystem"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 角色创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleFormRules"
        label-width="100px"
        class="role-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色名称" prop="name">
              <el-input
                v-model="roleForm.name"
                placeholder="请输入角色名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色编码" prop="code">
              <el-input
                v-model="roleForm.code"
                placeholder="请输入角色编码"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number
                v-model="roleForm.sortOrder"
                :min="1"
                :max="999"
                placeholder="排序"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="roleForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="ACTIVE" />
                <el-option label="禁用" value="INACTIVE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <role-permission-dialog
      v-model="permissionDialogVisible"
      :role="currentRole"
      :permissions="permissions"
      @submit="handlePermissionAssigned"
    />

    <!-- 角色模板对话框 -->
    <role-template-dialog
      v-model="templateDialogVisible"
      @template-selected="handleTemplateSelected"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UserFilled, Collection, Lock, Plus, User, Search, ArrowDown,
  Refresh, Edit, Delete, Key, Star
} from '@element-plus/icons-vue'
import { roleService } from '@/api/roleService'
import { permissionService } from '@/api/permissionService'
import { useAuthStore } from '@/stores/auth'
import RolePermissionDialog from '@/components/admin/RolePermissionDialog.vue'
import RoleTemplateDialog from '@/components/admin/RoleTemplateDialog.vue'

// 使用认证store
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const roles = ref([])
const selectedRoles = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const roleFormRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  isSystem: '',
  status: '',
  sortBy: 'sortOrder',
  sortDir: 'asc'
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const roleStats = reactive({
  total: 0,
  system: 0,
  custom: 0,
  totalUsers: 0
})

// 角色表单数据
const roleForm = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  sortOrder: 1,
  status: 'ACTIVE'
})

// 表单验证规则
const roleFormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '角色编码只能包含大写字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '排序必须在 1 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑角色' : '创建角色'
})

  // 方法
  const fetchRoles = async () => {
    loading.value = true
    try {
      // 使用增强的查询方法
      const response = await roleService.getRolesWithFilters({
        keyword: searchForm.keyword,
        isSystem: searchForm.isSystem,
        status: searchForm.status,
        page: pagination.page - 1, // 后端使用0基索引
        size: pagination.size,
        sortBy: searchForm.sortBy || 'sortOrder',
        sortDir: searchForm.sortDir || 'asc'
      })

      if (response.success) {
        roles.value = response.data?.content ?? []
        pagination.total = response.data?.totalElements ?? 0
      } else {
        ElMessage.error(response.message || '获取角色列表失败')
        roles.value = []
        pagination.total = 0
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      handleApiError(error)
      roles.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // API错误处理
  const handleApiError = (error) => {
    if (error.response?.status === 403) {
      ElMessage.error('没有权限访问角色列表，请联系管理员')
    } else if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      authStore.logout()
    } else {
      ElMessage.error(error.response?.data?.message || '操作失败')
    }
  }

// 移除 updateStats 函数，改用后端统计接口

// 获取角色统计信息（如果后端提供专门的统计接口）
const fetchRoleStats = async () => {
  try {
    const response = await roleService.getRoleStats()
    if (response.success) {
      Object.assign(roleStats, response.data)
    }
  } catch (error) {
    console.warn('获取角色统计失败，使用本地计算:', error)
    // 如果统计接口失败，仍然使用本地计算
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchRoles()
}

const handleCreate = () => {
  isEdit.value = false
  resetRoleForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  resetRoleForm()

  // 填充表单数据
  Object.assign(roleForm, {
    id: row.id,
    name: row.name,
    code: row.code,
    description: row.description || '',
    sortOrder: row.sortOrder || 1,
    status: row.status || 'ACTIVE'
  })

  dialogVisible.value = true
}

const resetRoleForm = () => {
  Object.assign(roleForm, {
    id: null,
    name: '',
    code: '',
    description: '',
    sortOrder: 1,
    status: 'ACTIVE'
  })

  // 清除表单验证
  if (roleFormRef.value) {
    roleFormRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    submitLoading.value = true

    let response
    if (isEdit.value) {
      // 更新角色
      response = await roleService.updateRole(roleForm.id, {
        name: roleForm.name,
        description: roleForm.description,
        sortOrder: roleForm.sortOrder,
        status: roleForm.status
      })
    } else {
      // 创建角色
      response = await roleService.createRole({
        name: roleForm.name,
        code: roleForm.code,
        description: roleForm.description,
        sortOrder: roleForm.sortOrder,
        status: roleForm.status
      })
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '角色更新成功' : '角色创建成功')
      dialogVisible.value = false
      fetchRoles()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交角色表单失败:', error)

    if (error.response?.status === 409) {
      ElMessage.error('角色编码已存在，请使用其他编码')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限执行此操作')
    } else {
      ElMessage.error(error.response?.data?.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } finally {
    submitLoading.value = false
  }
}

// 对话框状态
const permissionDialogVisible = ref(false)
const templateDialogVisible = ref(false)
const currentRole = ref(null)
const permissions = ref([])

// 处理创建命令
const handleCreateCommand = (command) => {
  if (command === 'template') {
    templateDialogVisible.value = true
  } else {
    handleCreate()
  }
}

// 处理模板选择
const handleTemplateSelected = (template) => {
  // 使用模板数据预填充表单
  roleForm.name = ''
  roleForm.code = ''
  roleForm.description = template.description
  roleForm.status = 'ACTIVE'
  roleForm.sortOrder = template.sortOrder || 1
  
  // 如果是自定义模板，不设置权限
  if (template.code !== 'CUSTOM') {
    roleForm.permissionIds = template.permissionCodes
  }
  
  dialogVisible.value = true
}

const handlePermissions = async (row) => {
  currentRole.value = row
  
  try {
    // 获取所有权限列表
    const response = await permissionService.getAllPermissions()
    if (response.success) {
      permissions.value = response.data
      permissionDialogVisible.value = true
    } else {
      ElMessage.error('获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    handleApiError(error)
  }
}

// 权限分配完成回调
const handlePermissionAssigned = () => {
  fetchRoles() // 刷新角色列表以更新权限计数
}

const handleUsers = (row) => {
  ElMessage.info(`用户管理: ${row.name}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 ${row.name} 吗？此操作不可撤销！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除API
    const response = await roleService.deleteRole(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      fetchRoles()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消删除')
    } else {
      console.error('删除角色失败:', error)

      if (error.response?.status === 403) {
        ElMessage.error('没有权限删除角色')
      } else if (error.response?.status === 409) {
        ElMessage.error('该角色下还有用户，无法删除')
      } else {
        ElMessage.error(error.response?.data?.message || '删除失败')
      }
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedRoles.value = selection
}

const handleBatchAction = async (command) => {
  if (command === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRoles.value.length} 个角色吗？此操作不可撤销！`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      // 获取选中角色的ID
      const roleIds = selectedRoles.value.map(role => role.id)

      // 调用批量删除API
      const response = await roleService.deleteRoles(roleIds)

      if (response.success) {
        ElMessage.success('批量删除成功')
        selectedRoles.value = [] // 清空选择
        fetchRoles()
      } else {
        ElMessage.error(response.message || '批量删除失败')
      }
    } catch (error) {
      if (error === 'cancel') {
        ElMessage.info('已取消删除')
      } else {
        console.error('批量删除角色失败:', error)

        if (error.response?.status === 403) {
          ElMessage.error('没有权限删除角色')
        } else if (error.response?.status === 409) {
          ElMessage.error('部分角色下还有用户，无法删除')
        } else {
          ElMessage.error(error.response?.data?.message || '批量删除失败')
        }
      }
    }
  }
}

const isRowSelectable = (row) => {
  return !row.isSystem
}

const getRowClassName = ({ row }) => {
  return row.isSystem ? 'system-row' : ''
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchRoles()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchRoles()
}

// 生命周期
onMounted(() => {
  fetchRoles()
  fetchRoleStats() // 调用统计接口
})
</script>

<style scoped>
.role-manage-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.system {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.custom {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.users {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

/* 工具栏 */
.toolbar-card {
  margin-bottom: 20px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-area {
  display: flex;
  gap: 12px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.type-select,
.status-select {
  width: 150px;
}

.search-area {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .search-input,
  .type-select,
  .status-select {
    width: 100%;
  }
}

.action-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格 */
.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.role-table {
  border-radius: 8px;
  overflow: hidden;
}

.role-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.role-table :deep(.system-row) {
  background: #fef9e7;
}

/* 角色信息 */
.role-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.system-icon {
  color: #f56c6c;
  font-size: 20px;
}

.custom-icon {
  color: #409eff;
  font-size: 20px;
}

.role-details {
  flex: 1;
}

.role-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.role-code {
  font-size: 12px;
  color: #7f8c8d;
  font-family: 'Courier New', monospace;
}

/* 描述 */
.role-description {
  color: #5a6c7d;
  font-size: 14px;
  line-height: 1.4;
}

/* 排序 */
.sort-order {
  font-weight: 600;
  color: #409eff;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 对话框样式 */
.role-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-area {
    flex-wrap: wrap;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .role-manage-container {
    padding: 10px;
  }

  .stats-cards :deep(.el-col) {
    margin-bottom: 10px;
  }

  .role-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
