package zh.backend.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zh.backend.entity.Permission;

import java.util.List;
import java.util.Optional;

/**
 * 权限数据访问接口
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    /**
     * 根据权限编码查找权限
     */
    Optional<Permission> findByCode(String code);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 根据资源类型查找权限
     */
    List<Permission> findByResourceType(Permission.ResourceType resourceType);

    /**
     * 根据状态查找权限
     */
    List<Permission> findByStatus(Permission.PermissionStatus status);

    /**
     * 查找系统内置权限
     */
    List<Permission> findByIsSystemTrue();

    /**
     * 查找非系统权限
     */
    List<Permission> findByIsSystemFalse();

    /**
     * 根据父权限ID查找子权限
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 查找顶级权限（无父权限）
     */
    @Query("SELECT p FROM Permission p WHERE p.parentId IS NULL ORDER BY p.sortOrder ASC, p.name ASC")
    List<Permission> findTopLevelPermissions();

    /**
     * 根据层级查找权限
     */
    List<Permission> findByLevel(Integer level);

    /**
     * 根据角色ID查找权限
     */
    @Query("SELECT p FROM Permission p JOIN p.roles r WHERE r.id = :roleId")
    List<Permission> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查找权限（包括角色权限和直接权限）
     */
    @Query("SELECT DISTINCT p FROM Permission p " +
           "LEFT JOIN p.roles r " +
           "LEFT JOIN r.users u " +
           "LEFT JOIN p.userPermissions up " +
           "LEFT JOIN up.user upu " +
           "WHERE (u.id = :userId) OR (upu.id = :userId AND up.status = 'ACTIVE' AND up.permissionType = 'GRANT')")
    List<Permission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据资源路径和HTTP方法查找权限
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "p.resourcePath = :resourcePath AND " +
           "(p.httpMethod = :httpMethod OR p.httpMethod = 'ALL')")
    List<Permission> findByResourcePathAndHttpMethod(
        @Param("resourcePath") String resourcePath, 
        @Param("httpMethod") Permission.HttpMethod httpMethod
    );

    /**
     * 分页查询权限（支持关键字搜索）
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "p.name LIKE %:keyword% OR " +
           "p.code LIKE %:keyword% OR " +
           "p.description LIKE %:keyword% OR " +
           "p.resourcePath LIKE %:keyword%)")
    Page<Permission> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 构建权限树结构
     */
    @Query("SELECT p FROM Permission p WHERE p.status = 'ACTIVE' ORDER BY p.level ASC, p.sortOrder ASC, p.name ASC")
    List<Permission> findActivePermissionsForTree();

    /**
     * 统计权限数量
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.status = :status")
    long countByStatus(@Param("status") Permission.PermissionStatus status);

    /**
     * 根据资源类型统计权限数量
     */
    @Query("SELECT p.resourceType, COUNT(p) FROM Permission p GROUP BY p.resourceType")
    List<Object[]> countByResourceType();

    /**
     * 查找权限及其子权限数量
     */
    @Query("SELECT p, COUNT(c) FROM Permission p LEFT JOIN Permission c ON c.parentId = p.id GROUP BY p")
    List<Object[]> findPermissionsWithChildrenCount();

    /**
     * 根据权限编码列表查找权限
     */
    @Query("SELECT p FROM Permission p WHERE p.code IN :codes")
    List<Permission> findByCodes(@Param("codes") List<String> codes);
}
