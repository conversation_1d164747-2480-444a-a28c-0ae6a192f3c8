/* Global Theme Variables */
:root {
  /* Primary Colors */
  --primary-color: #4f46e5;  /* Indigo-600 */
  --primary-hover: #4338ca;  /* Indigo-700 */
  --primary-light: #eef2ff;  /* Indigo-50 */
  --primary-lighter: #e0e7ff; /* Indigo-100 */
  
  /* Text Colors */
  --text-primary: #1f2937;   /* Gray-800 */
  --text-secondary: #4b5563;  /* Gray-600 */
  --text-muted: #6b7280;     /* Gray-500 */
  --text-light: #9ca3af;     /* Gray-400 */
  
  /* Background Colors */
  --bg-primary: #ffffff;     /* White */
  --bg-secondary: #f9fafb;   /* Gray-50 */
  --bg-tertiary: #f3f4f6;    /* Gray-100 */
  --bg-hover: #f3f4f6;       /* Gray-100 */
  
  /* Border Colors */
  --border-primary: #e5e7eb;  /* Gray-200 */
  --border-secondary: #d1d5db; /* Gray-300 */
  
  /* Status Colors */
  --success-color: #10b981;   /* Emerald-500 */
  --warning-color: #f59e0b;   /* Amber-500 */
  --danger-color: #ef4444;    /* Red-500 */
  --info-color: #3b82f6;      /* Blue-500 */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Apply theme to Element Plus components */
.el-menu {
  background-color: var(--bg-primary) !important;
  color: var(--text-secondary) !important;
}

.el-menu-item,
.el-sub-menu__title {
  color: var(--text-secondary) !important;
}

.el-menu-item:hover,
.el-sub-menu__title:hover {
  background-color: var(--bg-hover) !important;
  color: var(--text-primary) !important;
}

.el-menu-item.is-active {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* Ensure consistent header styling */
.el-header {
  background-color: var(--bg-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

/* Ensure consistent aside styling */
.el-aside {
  background-color: var(--bg-primary) !important;
  border-right: 1px solid var(--border-primary) !important;
}

/* Ensure consistent main content area styling */
.el-main {
  background-color: var(--bg-secondary) !important;
}

/* Button styles */
.el-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.el-button--primary:hover {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

/* Tag styles for consistency */
.el-tag--primary {
  background-color: var(--primary-lighter) !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-light) !important;
}

.el-tag--success {
  background-color: #d1fae5 !important;
  color: #065f46 !important;
  border-color: #a7f3d0 !important;
}

.el-tag--danger {
  background-color: #fee2e2 !important;
  color: #991b1b !important;
  border-color: #fecaca !important;
}

.el-tag--warning {
  background-color: #fef3c7 !important;
  color: #92400e !important;
  border-color: #fde68a !important;
}