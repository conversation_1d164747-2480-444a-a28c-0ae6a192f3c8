import api from './axios'

/**
 * @typedef {Object} Permission
 * @property {number} id - Permission ID
 * @property {string} name - Permission name
 * @property {string} code - Permission code
 * @property {string} description - Permission description
 * @property {'MENU'|'BUTTON'|'API'|'DATA'} resourceType - Resource type
 * @property {string} resourcePath - Resource path
 * @property {'GET'|'POST'|'PUT'|'DELETE'|'PATCH'|'ALL'} httpMethod - HTTP method
 * @property {number} parentId - Parent permission ID
 * @property {number} level - Permission level
 * @property {number} sortOrder - Sort order
 * @property {'ACTIVE'|'INACTIVE'} status - Permission status
 * @property {boolean} isSystem - Whether it's a system permission
 */

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Whether the request was successful
 * @property {string} message - Response message
 * @property {*} data - Response data
 */

/**
 * Permission Management API Service
 */
class PermissionService {
  constructor() {
    this.baseUrl = '/api/permissions'
  }

  /**
   * Get paginated permissions with filters
   * @param {Object} params Query parameters
   * @param {string} [params.keyword] Search keyword
   * @param {number} [params.page=0] Page number (0-based)
   * @param {number} [params.size=10] Page size
   * @param {string} [params.sortBy='id'] Sort field
   * @param {string} [params.sortDir='asc'] Sort direction
   * @param {string} [params.resourceType] Filter by resource type
   * @param {string} [params.status] Filter by status
   * @param {boolean} [params.isSystem] Filter system permissions
   * @returns {Promise<ApiResponse>} Paginated permission list
   */
  async getPermissions(params = {}) {
    try {
      const response = await api.get(this.baseUrl, { params })
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch permissions')
    }
  }

  /**
   * Get permission tree structure
   * @param {Object} params Query parameters
   * @param {string} [params.resourceType] Filter by resource type
   * @param {boolean} [params.includeInactive=false] Include inactive permissions
   * @returns {Promise<ApiResponse>} Permission tree
   */
  async getPermissionTree(params = {}) {
    try {
      const response = await api.get(`${this.baseUrl}/tree`, { params })
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch permission tree')
    }
  }

  /**
   * Get permission by ID
   * @param {number} id Permission ID
   * @returns {Promise<ApiResponse>} Permission details
   */
  async getPermissionById(id) {
    try {
      if (!id || id <= 0) throw new Error('Invalid permission ID')
      const response = await api.get(`${this.baseUrl}/${id}`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch permission details')
    }
  }

  /**
   * Create new permission
   * @param {Permission} permission Permission data
   * @returns {Promise<ApiResponse>} Created permission
   */
  async createPermission(permission) {
    try {
      this.validatePermissionData(permission)
      const response = await api.post(this.baseUrl, permission)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to create permission')
    }
  }

  /**
   * Update permission
   * @param {number} id Permission ID
   * @param {Permission} permission Updated permission data
   * @returns {Promise<ApiResponse>} Updated permission
   */
  async updatePermission(id, permission) {
    try {
      if (!id || id <= 0) throw new Error('Invalid permission ID')
      this.validatePermissionData(permission)
      const response = await api.put(`${this.baseUrl}/${id}`, permission)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to update permission')
    }
  }

  /**
   * Delete permission
   * @param {number} id Permission ID
   * @returns {Promise<ApiResponse>} Deletion result
   */
  async deletePermission(id) {
    try {
      if (!id || id <= 0) throw new Error('Invalid permission ID')
      const response = await api.delete(`${this.baseUrl}/${id}`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to delete permission')
    }
  }

  /**
   * Batch delete permissions
   * @param {number[]} ids Permission IDs
   * @returns {Promise<ApiResponse>} Batch deletion result
   */
  async batchDeletePermissions(ids) {
    try {
      if (!Array.isArray(ids) || ids.length === 0) {
        throw new Error('Invalid permission IDs')
      }
      const response = await api.delete(`${this.baseUrl}/batch`, { data: ids })
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to batch delete permissions')
    }
  }

  /**
   * Clear permission cache
   * @returns {Promise<ApiResponse>} Cache clearing result
   */
  async clearPermissionCache() {
    try {
      const response = await api.post(`${this.baseUrl}/clear-cache`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to clear permission cache')
    }
  }

  /**
   * Get permission statistics
   * @returns {Promise<ApiResponse>} Permission statistics
   */
  async getPermissionStatistics() {
    try {
      const response = await api.get(`${this.baseUrl}/statistics`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch permission statistics')
    }
  }

  /**
   * Check if permission code exists
   * @param {string} code Permission code
   * @returns {Promise<boolean>} Whether code exists
   */
  async checkPermissionExists(code) {
    try {
      if (!code) throw new Error('Permission code is required')
      const response = await api.get(`${this.baseUrl}/exists`, {
        params: { code }
      })
      return response.data?.data || false
    } catch (error) {
      throw this.handleError(error, 'Failed to check permission code')
    }
  }

  /**
   * Get user's direct permissions
   * @param {number} userId User ID
   * @returns {Promise<ApiResponse>} User's direct permissions
   */
  async getUserDirectPermissions(userId) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      const response = await api.get(`${this.baseUrl}/user/${userId}/direct`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch user direct permissions')
    }
  }

  /**
   * Get user's role permissions
   * @param {number} userId User ID
   * @returns {Promise<ApiResponse>} User's role permissions
   */
  async getUserRolePermissions(userId) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      const response = await api.get(`${this.baseUrl}/user/${userId}/role`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch user role permissions')
    }
  }

  /**
   * Get all user permissions
   * @param {number} userId User ID
   * @returns {Promise<ApiResponse>} All user permissions
   */
  async getAllUserPermissions(userId) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      const response = await api.get(`${this.baseUrl}/user/${userId}/all`)
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch all user permissions')
    }
  }

  /**
   * Grant permission to user
   * @param {number} userId User ID
   * @param {number} permissionId Permission ID
   * @param {string} [expiresAt] Expiration date
   * @returns {Promise<ApiResponse>} Grant result
   */
  async grantPermissionToUser(userId, permissionId, expiresAt = null) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      if (!permissionId || permissionId <= 0) throw new Error('Invalid permission ID')

      const params = { permissionId }
      if (expiresAt) params.expiresAt = expiresAt

      const response = await api.post(
        `${this.baseUrl}/user/${userId}/grant`,
        null,
        { params }
      )
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to grant permission to user')
    }
  }

  /**
   * Revoke permission from user
   * @param {number} userId User ID
   * @param {number} permissionId Permission ID
   * @returns {Promise<ApiResponse>} Revocation result
   */
  async revokePermissionFromUser(userId, permissionId) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      if (!permissionId || permissionId <= 0) throw new Error('Invalid permission ID')

      const response = await api.delete(
        `${this.baseUrl}/user/${userId}/revoke`,
        { params: { permissionId } }
      )
      return response.data
    } catch (error) {
      throw this.handleError(error, 'Failed to revoke permission from user')
    }
  }

  /**
   * Check if user has permission
   * @param {number} userId User ID
   * @param {string} permissionCode Permission code
   * @returns {Promise<boolean>} Whether user has permission
   */
  async checkUserPermission(userId, permissionCode) {
    try {
      if (!userId || userId <= 0) throw new Error('Invalid user ID')
      if (!permissionCode) throw new Error('Permission code is required')

      const response = await api.get(
        `${this.baseUrl}/user/${userId}/check`,
        { params: { permissionCode } }
      )
      return response.data?.data || false
    } catch (error) {
      throw this.handleError(error, 'Failed to check user permission')
    }
  }

  /**
   * Get all permissions without pagination
   * @param {Object} params Query parameters
   * @param {string} [params.resourceType] Filter by resource type
   * @param {string} [params.status] Filter by status
   * @param {boolean} [params.isSystem] Filter system permissions
   * @returns {Promise<ApiResponse>} All permissions
   */
  async getAllPermissions(params = {}) {
    try {
      const allPermissions = []
      let page = 0
      const size = 100 // Maximum allowed page size
      let hasMore = true

      while (hasMore) {
        const queryParams = {
          ...params,
          page,
          size,
          sortBy: 'sortOrder',
          sortDir: 'asc'
        }

        const response = await api.get(this.baseUrl, { params: queryParams })

        if (response.data?.success && response.data?.data?.content) {
          const content = response.data.data.content
          allPermissions.push(...content)

          // Check if there are more pages
          const totalPages = response.data.data.totalPages || 0
          hasMore = page + 1 < totalPages
          page++
        } else {
          hasMore = false
        }
      }

      return {
        success: true,
        data: allPermissions,
        message: 'Successfully fetched all permissions'
      }
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch all permissions')
    }
  }

  /**
   * Validate permission data
   * @param {Permission} permission Permission data to validate
   * @throws {Error} Validation error
   */
  validatePermissionData(permission) {
    const errors = []

    if (!permission.name?.trim()) {
      errors.push('Permission name is required')
    }

    if (!permission.code?.trim()) {
      errors.push('Permission code is required')
    } else if (!/^[A-Z][A-Z0-9_]*$/.test(permission.code)) {
      errors.push('Permission code must start with uppercase letter and contain only uppercase letters, numbers, and underscores')
    }

    if (!permission.resourceType) {
      errors.push('Resource type is required')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }

  /**
   * Handle API error
   * @param {Error} error Original error
   * @param {string} defaultMessage Default error message
   * @returns {Error} Processed error
   */
  handleError(error, defaultMessage) {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    }
    return new Error(error.message || defaultMessage)
  }
}

export const permissionService = new PermissionService()
export default permissionService
