# 动态权限管理系统设计文档

## 📋 系统概述

本项目实现了一个完整的动态权限管理系统，基于RBAC（基于角色的访问控制）模型，支持运行时动态权限分配和细粒度权限控制。

## 🏗️ 系统架构

### 后端架构 (Spring Boot 3.3.5 + Java 17)

#### 1. 数据库设计

**核心表结构：**
- `users` - 用户表
- `roles` - 角色表  
- `permissions` - 权限表
- `user_roles` - 用户角色关联表
- `role_permissions` - 角色权限关联表
- `user_permissions` - 用户直接权限表（支持特殊权限分配）
- `permission_policies` - 权限策略表（动态权限规则）
- `permission_logs` - 权限操作日志表

#### 2. 实体设计

**主要实体类：**
- `User` - 用户实体，支持多状态（ACTIVE/INACTIVE/LOCKED）
- `Role` - 角色实体，支持系统内置和自定义角色
- `Permission` - 权限实体，支持层级结构和多种资源类型
- `UserPermission` - 用户直接权限，支持授予/拒绝类型
- `PermissionPolicy` - 权限策略，支持SpEL表达式条件判断

#### 3. 服务层设计

**核心服务：**
- `PermissionService` - 权限管理服务，支持权限缓存和动态检查
- `RoleService` - 角色管理服务
- `UserService` - 用户管理服务

#### 4. 权限控制

**注解支持：**
- `@RequirePermission` - 方法级权限验证
- `@RequireRole` - 方法级角色验证

**AOP切面：**
- `PermissionAspect` - 权限验证切面，支持ANY/ALL验证模式

### 前端架构 (Vue 3 + Element Plus)

#### 1. 权限管理页面
- **权限管理** (`PermissionManageView.vue`) - 权限CRUD、树形结构展示
- **角色管理** (`RoleManageView.vue`) - 角色CRUD、权限分配
- **用户管理** (`UserManageView.vue`) - 用户CRUD、角色分配

#### 2. 权限指令
- `v-can` - 权限指令，用于前端权限控制

## 🚀 核心功能

### 1. 动态权限分配
- ✅ 运行时权限分配和撤销
- ✅ 支持用户直接权限（覆盖角色权限）
- ✅ 权限继承和层级管理
- ✅ 权限过期时间控制

### 2. 细粒度权限控制
- ✅ 菜单级权限控制
- ✅ 按钮级权限控制  
- ✅ API接口权限控制
- ✅ 数据级权限控制

### 3. 权限策略引擎
- ✅ 基于SpEL表达式的动态权限规则
- ✅ 条件权限（如时间、IP等限制）
- ✅ 权限策略优先级管理

### 4. 权限缓存机制
- ✅ 用户权限缓存（提高性能）
- ✅ 权限变更时自动刷新缓存
- ✅ 分布式缓存支持

### 5. 审计日志
- ✅ 权限操作日志记录
- ✅ 用户访问日志
- ✅ 权限变更历史追踪

## 📊 权限模型

### RBAC模型扩展

```
用户 (User) ←→ 角色 (Role) ←→ 权限 (Permission)
     ↓                              ↑
直接权限 (UserPermission) ──────────┘
     ↓
权限策略 (PermissionPolicy)
```

### 权限类型

1. **资源类型**
   - `MENU` - 菜单权限
   - `BUTTON` - 按钮权限
   - `API` - 接口权限
   - `DATA` - 数据权限

2. **HTTP方法**
   - `GET`, `POST`, `PUT`, `DELETE`, `PATCH`, `ALL`

3. **权限状态**
   - `ACTIVE` - 激活
   - `INACTIVE` - 未激活

## 🔧 配置说明

### 后端配置 (application.yml)

```yaml
permission:
  jwt:
    secret: mySecretKey123456789012345678901234567890
    expiration: 86400000  # 24小时
  cache:
    enabled: true
    ttl: 3600  # 缓存时间（秒）
  security:
    permit-all-paths:
      - /api/auth/login
      - /api/auth/register
    admin-paths:
      - /api/admin/**
      - /api/users/**
```

### 数据库配置

支持H2（开发）和MySQL（生产）：

```yaml
spring:
  datasource:
    # 开发环境 - H2
    url: jdbc:h2:mem:testdb
    # 生产环境 - MySQL  
    # url: *********************************************
```

## 🎯 使用示例

### 1. 后端权限验证

```java
@RestController
@RequestMapping("/api/users")
@RequirePermission("USER_MANAGEMENT")
public class UserController {
    
    @GetMapping
    @RequirePermission("USER_LIST")
    public ResponseEntity<Page<User>> getUsers() {
        // 获取用户列表
    }
    
    @PostMapping
    @RequirePermission("USER_CREATE")
    public ResponseEntity<User> createUser(@RequestBody User user) {
        // 创建用户
    }
}
```

### 2. 前端权限控制

```vue
<template>
  <div>
    <!-- 按钮权限控制 -->
    <el-button v-can="'USER_CREATE'" @click="createUser">
      创建用户
    </el-button>
    
    <!-- 菜单权限控制 -->
    <el-menu-item v-can="'USER_MANAGEMENT'">
      用户管理
    </el-menu-item>
  </div>
</template>
```

### 3. 动态权限分配

```java
// 为用户直接分配权限
permissionService.grantPermissionToUser(userId, permissionId, grantedBy);

// 撤销用户权限
permissionService.revokePermissionFromUser(userId, permissionId);

// 检查用户权限
boolean hasPermission = permissionService.hasPermission(userId, "USER_CREATE");
```

## 🔒 安全特性

1. **JWT认证** - 无状态认证机制
2. **密码加密** - BCrypt加密存储
3. **权限缓存** - 提高权限检查性能
4. **操作审计** - 完整的操作日志记录
5. **权限策略** - 支持复杂的权限规则

## 📈 性能优化

1. **权限缓存** - Redis缓存用户权限信息
2. **懒加载** - 权限树按需加载
3. **批量操作** - 支持批量权限分配
4. **索引优化** - 数据库查询索引优化

## 🚀 部署说明

### 开发环境
1. 启动后端：`mvn spring-boot:run`
2. 启动前端：`npm run dev`
3. 访问：http://localhost:5173

### 生产环境
1. 构建后端：`mvn clean package`
2. 构建前端：`npm run build`
3. 部署JAR文件和静态资源

## 📝 默认账户

- **超级管理员**
  - 用户名：`admin`
  - 密码：`admin123`
  - 权限：所有权限

## 🔄 扩展性

系统设计支持以下扩展：

1. **多租户支持** - 可扩展为多租户权限系统
2. **OAuth2集成** - 可集成第三方认证
3. **微服务架构** - 可拆分为独立的权限服务
4. **国际化支持** - 支持多语言界面
5. **移动端适配** - 可扩展移动端权限控制

## 📚 相关文档

- [API文档](./docs/api.md)
- [数据库设计](./docs/database.md)
- [部署指南](./docs/deployment.md)
- [开发指南](./docs/development.md)
