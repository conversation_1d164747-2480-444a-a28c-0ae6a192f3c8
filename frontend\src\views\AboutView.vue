<template>
  <div class="about-container">
    <h1>关于我们</h1>
    
    <div class="about-content">
      <section class="about-section">
        <h2>系统简介</h2>
        <p>这是一个基于 Spring Boot 和 Vue.js 开发的认证授权系统，提供用户注册、登录、权限管理等功能。</p>
        <p>本系统采用了 JWT (JSON Web Token) 进行身份验证，并实现了基于角色的访问控制（RBAC）。</p>
      </section>
      
      <section class="about-section">
        <h2>主要功能</h2>
        <ul>
          <li><strong>用户认证</strong> - 支持用户名或邮箱登录</li>
          <li><strong>用户注册</strong> - 新用户可以通过注册页面创建账户</li>
          <li><strong>权限管理</strong> - 基于角色的权限系统</li>
          <li><strong>个人信息</strong> - 用户可以查看个人信息</li>
          <li><strong>令牌刷新</strong> - 支持JWT令牌的刷新</li>
        </ul>
      </section>
      
      <section class="about-section">
        <h2>技术栈</h2>
        <div class="tech-stack">
          <div class="tech-card">
            <h3>前端</h3>
            <ul>
              <li>Vue 3</li>
              <li>Vue Router</li>
              <li>Pinia (状态管理)</li>
              <li>Axios (HTTP 客户端)</li>
              <li>Vite (构建工具)</li>
            </ul>
          </div>
          
          <div class="tech-card">
            <h3>后端</h3>
            <ul>
              <li>Spring Boot</li>
              <li>Spring Security</li>
              <li>Spring Data JPA</li>
              <li>JWT 认证</li>
              <li>MySQL/H2 数据库</li>
            </ul>
          </div>
        </div>
      </section>
      
      <section class="about-section">
        <h2>联系我们</h2>
        <p>如果您有任何问题或建议，请发送邮件至：<a href="mailto:<EMAIL>"><EMAIL></a></p>
      </section>
    </div>
  </div>
</template>

<style scoped>
.about-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.about-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.about-section {
  margin-bottom: 40px;
}

.about-section:last-child {
  margin-bottom: 0;
}

.about-section h2 {
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.about-section ul {
  padding-left: 20px;
}

.about-section li {
  margin-bottom: 8px;
}

.tech-stack {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.tech-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tech-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
}

.tech-card ul {
  padding-left: 20px;
  margin: 0;
}

a {
  color: #007bff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
