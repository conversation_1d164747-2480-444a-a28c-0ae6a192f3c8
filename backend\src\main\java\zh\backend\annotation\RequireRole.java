package zh.backend.annotation;

import java.lang.annotation.*;

/**
 * 角色验证注解
 * 用于方法级别的角色控制
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {

    /**
     * 需要的角色编码
     */
    String[] value() default {};

    /**
     * 角色编码（与value等价，提供更明确的语义）
     */
    String[] roles() default {};

    /**
     * 角色验证模式
     */
    RoleMode mode() default RoleMode.ANY;

    /**
     * 是否允许匿名访问
     */
    boolean allowAnonymous() default false;

    /**
     * 角色验证失败时的错误消息
     */
    String message() default "角色权限不足，无法访问该资源";

    /**
     * 角色验证模式枚举
     */
    enum RoleMode {
        /**
         * 任意一个角色满足即可
         */
        ANY,
        
        /**
         * 所有角色都必须满足
         */
        ALL
    }
}
