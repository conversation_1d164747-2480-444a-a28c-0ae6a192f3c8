import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import DashboardView from '../views/DashboardView.vue'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import { useAuthStore } from '@/stores/auth'
import { canAccessRoute, PERMISSIONS } from '@/utils/permissions'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/dashboard',
      meta: { requiresAuth: true },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      meta: { requiresAuth: true, title: '仪表盘' },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true, title: '个人资料' },
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true, title: '关于' },
    },

    // 管理员路由
    {
      path: '/admin',
      name: 'admin',
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/UserManageView.vue'),
          meta: { requiresAuth: true, requiresPermission: PERMISSIONS.USER_LIST, title: '用户管理' },
        },
        {
          path: 'roles',
          name: 'admin-roles',
          component: () => import('../views/admin/RoleManageView.vue'),
          meta: { requiresAuth: true, requiresPermission: PERMISSIONS.ROLE_LIST, title: '角色管理' },
        },
        {
          path: 'permissions',
          name: 'admin-permissions',
          component: () => import('../views/admin/PermissionManageView.vue'),
          meta: { requiresAuth: true, requiresPermission: PERMISSIONS.PERMISSION_LIST, title: '权限管理' },
        },



      ],
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView,
    }
  ],
})

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // 如果有token但未认证，尝试初始化认证
  if (!authStore.token && localStorage.getItem('token')) {
    await authStore.initializeAuth();
  }

  const isAuthenticated = authStore.isAuthenticated;
  const user = authStore.user;

  // 检查是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated) {
    next({ name: 'login' });
    return;
  }

  // 已登录用户访问登录/注册页面，重定向到仪表盘
  if ((to.name === 'login' || to.name === 'register') && isAuthenticated) {
    next({ name: 'dashboard' });
    return;
  }

  // 检查管理员权限
  if (to.meta.requiresAdmin && isAuthenticated) {
    const userRoles = user?.roles || [];
    if (!userRoles.includes('SUPER_ADMIN') && !userRoles.includes('SYSTEM_ADMIN')) {
      next({ name: 'dashboard' });
      return;
    }
  }

  // 检查路由访问权限
  if (isAuthenticated && !canAccessRoute(user, to)) {
    next({ name: 'dashboard' });
    return;
  }

  next();
})

export default router
