# 动态权限管理系统前端

基于Vue 3 + Element Plus的动态权限管理系统前端应用。

## 🚀 快速启动

### 前置要求

- Node.js 16+
- npm 8+ 或 yarn 1.22+

### 启动步骤

1. **安装依赖**

   ```bash
   npm install
   ```

2. **启动开发服务器**

   ```bash
   npm run dev
   ```

   或者在Windows上运行：
   ```bash
   start.bat
   ```

3. **访问应用**

   打开浏览器访问：http://localhost:5173

## 🔧 环境配置

### 开发环境

创建 `.env.development` 文件：

```env
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=动态权限管理系统
```

### 生产环境

创建 `.env.production` 文件：

```env
VITE_API_BASE_URL=https://your-api-domain.com
VITE_APP_TITLE=动态权限管理系统
```

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API服务
│   │   ├── axios.js       # Axios配置
│   │   ├── permissionService.js  # 权限API
│   │   ├── roleService.js        # 角色API
│   │   └── userService.js        # 用户API
│   ├── components/        # 公共组件
│   ├── directives/        # 自定义指令
│   │   └── permission.js  # 权限指令
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   │   └── auth.js       # 认证状态
│   ├── utils/            # 工具函数
│   │   └── permissions.js # 权限工具
│   ├── views/            # 页面组件
│   │   ├── admin/        # 管理页面
│   │   │   ├── PermissionManageView.vue
│   │   │   ├── RoleManageView.vue
│   │   │   └── UserManageView.vue
│   │   └── ...
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── package.json
└── vite.config.js        # Vite配置
```

## 🔐 权限系统

### 权限指令

使用 `v-can` 指令控制元素显示：

```vue
<!-- 单个权限 -->
<el-button v-can="'USER_CREATE'">创建用户</el-button>

<!-- 多个权限（任一） -->
<el-button v-can:any="['USER_CREATE', 'USER_UPDATE']">操作用户</el-button>

<!-- 多个权限（全部） -->
<el-button v-can:all="['USER_CREATE', 'USER_UPDATE']">高级操作</el-button>
```

### 权限检查函数

```javascript
import { hasPermission, hasRole } from '@/utils/permissions'

// 检查权限
if (hasPermission(user, 'USER_CREATE')) {
  // 用户有创建权限
}

// 检查角色
if (hasRole(user, 'SUPER_ADMIN')) {
  // 用户是超级管理员
}
```

### 路由权限

```javascript
{
  path: '/admin/users',
  component: UserManageView,
  meta: {
    requiresAuth: true,
    requiresPermission: 'USER_LIST'
  }
}
```

## 🎨 主要功能

### 认证系统
- ✅ 用户登录/注册
- ✅ 用户登出
- ✅ 自动认证状态管理
- ✅ 路由权限守卫

### 用户管理
- ✅ 用户列表查看
- ✅ 用户创建/编辑/删除
- ✅ 用户角色分配
- ✅ 用户状态管理
- ✅ 密码重置

### 角色管理
- ✅ 角色列表查看
- ✅ 角色创建/编辑/删除
- ✅ 角色权限分配
- ✅ 角色用户管理

### 权限管理
- ✅ 权限列表查看
- ✅ 权限创建/编辑/删除
- ✅ 权限树形结构
- ✅ 权限状态管理

### 个人中心
- ✅ 个人信息查看/编辑
- ✅ 密码修改
- ✅ 用户资料管理

### 系统功能
- ✅ 仪表盘概览
- ✅ 动态菜单生成
- ✅ 权限指令控制
- ✅ 响应式布局

## 🛠️ 开发指南

### 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.js` 中添加路由
3. 配置权限要求（如需要）

### 添加新API

1. 在 `src/api/` 下创建服务文件
2. 使用统一的axios实例
3. 处理错误和加载状态

### 权限配置

1. 在 `src/utils/permissions.js` 中定义权限常量
2. 使用权限指令或函数进行检查
3. 在路由meta中配置权限要求

## 📦 构建部署

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 🔍 故障排除

1. **开发服务器启动失败**
   - 检查Node.js版本
   - 清除node_modules重新安装
   - 检查端口是否被占用

2. **API请求失败**
   - 检查后端服务是否启动
   - 验证API_BASE_URL配置
   - 检查网络连接

3. **权限功能异常**
   - 检查用户登录状态
   - 验证权限配置
   - 查看浏览器控制台错误

## 📝 注意事项

- 开发环境会自动代理API请求到后端
- 权限检查在前端仅用于UI控制，安全验证在后端
- 建议使用Chrome DevTools进行调试
- 生产环境需要配置正确的API地址
