package zh.backend.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zh.backend.entity.Permission;

import java.util.List;

/**
 * 权限 MyBatis Mapper
 */
@Mapper
public interface PermissionMapper {

    /**
     * 根据ID查找权限
     */
    Permission findById(@Param("id") Long id);

    /**
     * 根据权限编码查找权限
     */
    Permission findByCode(@Param("code") String code);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByCode(@Param("code") String code);

    /**
     * 查找所有权限
     */
    List<Permission> findAll();

    /**
     * 根据资源类型查找权限
     */
    List<Permission> findByResourceType(@Param("resourceType") String resourceType);

    /**
     * 根据状态查找权限
     */
    List<Permission> findByStatus(@Param("status") String status);

    /**
     * 查找系统内置权限
     */
    List<Permission> findByIsSystemTrue();

    /**
     * 查找非系统权限
     */
    List<Permission> findByIsSystemFalse();

    /**
     * 根据父权限ID查找子权限
     */
    List<Permission> findByParentId(@Param("parentId") Long parentId);

    /**
     * 查找顶级权限（无父权限）
     */
    List<Permission> findTopLevelPermissions();

    /**
     * 根据层级查找权限
     */
    List<Permission> findByLevel(@Param("level") Integer level);

    /**
     * 根据角色ID查找权限
     */
    List<Permission> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查找权限（包括角色权限和直接权限）
     */
    List<Permission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据资源路径和HTTP方法查找权限
     */
    List<Permission> findByResourcePathAndHttpMethod(
        @Param("resourcePath") String resourcePath, 
        @Param("httpMethod") String httpMethod
    );

    /**
     * 分页查询权限（支持关键字搜索）
     */
    List<Permission> findByKeyword(
        @Param("keyword") String keyword,
        @Param("offset") int offset,
        @Param("size") int size,
        @Param("sortBy") String sortBy,
        @Param("sortDir") String sortDir
    );

    /**
     * 统计关键字搜索结果数量
     */
    long countByKeyword(@Param("keyword") String keyword);

    /**
     * 构建权限树结构
     */
    List<Permission> findActivePermissionsForTree();

    /**
     * 统计权限数量
     */
    long countByStatus(@Param("status") String status);

    /**
     * 根据资源类型统计权限数量
     */
    List<Object[]> countByResourceType();

    /**
     * 查找权限及其子权限数量
     */
    List<Object[]> findPermissionsWithChildrenCount();

    /**
     * 插入权限
     */
    int insertPermission(Permission permission);

    /**
     * 更新权限
     */
    int updatePermission(Permission permission);

    /**
     * 根据ID删除权限
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除权限
     */
    int deleteByIds(@Param("ids") List<Long> ids);
}
