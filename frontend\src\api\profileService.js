import api from './axios'

const API_URL = '/api/profile'

/**
 * 个人资料API服务
 * 配合重新设计的后端ProfileController
 * 
 * @version 2.0
 * <AUTHOR> Team
 */
export const profileService = {
  // ==================== 个人信息管理 ====================

  /**
   * 获取当前用户的个人信息
   * @returns {Promise<Object>} 当前用户信息
   */
  async getCurrentUserProfile() {
    try {
      const response = await api.get(API_URL)
      return response.data
    } catch (error) {
      console.error('获取个人信息失败:', error)
      throw this.handleApiError(error, '获取个人信息失败')
    }
  },

  /**
   * 更新当前用户的个人信息
   * @param {Object} profileData - 个人信息数据
   * @param {string} profileData.realName - 真实姓名
   * @param {string} profileData.email - 邮箱地址
   * @param {string} profileData.phone - 手机号码
   * @returns {Promise<Object>} 更新结果
   */
  async updateProfile(profileData) {
    try {
      if (!profileData) {
        throw new Error('个人信息数据不能为空')
      }

      // 验证数据
      const validation = this.validateProfileData(profileData)
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const response = await api.put(API_URL, profileData)
      return response.data
    } catch (error) {
      console.error('更新个人信息失败:', error)
      throw this.handleApiError(error, '更新个人信息失败')
    }
  },

  /**
   * 修改当前用户密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.currentPassword - 当前密码
   * @param {string} passwordData.newPassword - 新密码
   * @returns {Promise<Object>} 修改结果
   */
  async changePassword(passwordData) {
    try {
      if (!passwordData?.currentPassword || !passwordData?.newPassword) {
        throw new Error('当前密码和新密码不能为空')
      }

      if (passwordData.newPassword.length < 6) {
        throw new Error('新密码长度至少为6位')
      }

      if (passwordData.newPassword.length > 100) {
        throw new Error('新密码长度不能超过100个字符')
      }

      const response = await api.put(`${API_URL}/password`, passwordData)
      return response.data
    } catch (error) {
      console.error('修改密码失败:', error)
      throw this.handleApiError(error, '修改密码失败')
    }
  },

  // ==================== 权限和角色信息 ====================

  /**
   * 获取当前用户的角色信息
   * @returns {Promise<Array>} 用户角色列表
   */
  async getCurrentUserRoles() {
    try {
      const response = await api.get(`${API_URL}/roles`)
      return response.data
    } catch (error) {
      console.error('获取用户角色失败:', error)
      throw this.handleApiError(error, '获取用户角色失败')
    }
  },

  /**
   * 获取当前用户的权限信息
   * @returns {Promise<Object>} 用户权限信息
   */
  async getCurrentUserPermissions() {
    try {
      const response = await api.get(`${API_URL}/permissions`)
      return response.data
    } catch (error) {
      console.error('获取用户权限失败:', error)
      throw this.handleApiError(error, '获取用户权限失败')
    }
  },

  /**
   * 获取当前用户的安全信息
   * @returns {Promise<Object>} 安全信息
   */
  async getSecurityInfo() {
    try {
      const response = await api.get(`${API_URL}/security`)
      return response.data
    } catch (error) {
      console.error('获取安全信息失败:', error)
      throw this.handleApiError(error, '获取安全信息失败')
    }
  },

  // ==================== 工具方法 ====================

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {string} defaultMessage - 默认错误消息
   * @returns {Error} 处理后的错误
   */
  handleApiError(error, defaultMessage) {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    }
    if (error.message) {
      return new Error(error.message)
    }
    return new Error(defaultMessage)
  },

  /**
   * 验证个人信息数据
   * @param {Object} profileData - 个人信息数据
   * @returns {Object} 验证结果
   */
  validateProfileData(profileData) {
    const errors = []
    
    if (profileData.realName && profileData.realName.length > 100) {
      errors.push('真实姓名长度不能超过100个字符')
    }
    
    if (profileData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(profileData.email)) {
        errors.push('邮箱格式不正确')
      }
      if (profileData.email.length > 100) {
        errors.push('邮箱长度不能超过100个字符')
      }
    }
    
    if (profileData.phone) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(profileData.phone)) {
        errors.push('手机号格式不正确')
      }
      if (profileData.phone.length > 20) {
        errors.push('手机号长度不能超过20个字符')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 验证密码数据
   * @param {Object} passwordData - 密码数据
   * @returns {Object} 验证结果
   */
  validatePasswordData(passwordData) {
    const errors = []
    
    if (!passwordData.currentPassword) {
      errors.push('当前密码不能为空')
    }
    
    if (!passwordData.newPassword) {
      errors.push('新密码不能为空')
    } else {
      if (passwordData.newPassword.length < 6) {
        errors.push('新密码长度至少为6位')
      }
      if (passwordData.newPassword.length > 100) {
        errors.push('新密码长度不能超过100个字符')
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 格式化用户状态
   * @param {string} status - 用户状态
   * @returns {string} 格式化后的状态
   */
  formatUserStatus(status) {
    const statusMap = {
      'ACTIVE': '活跃',
      'INACTIVE': '未激活',
      'LOCKED': '锁定'
    }
    return statusMap[status] || '未知'
  },

  /**
   * 格式化角色名称
   * @param {string} roleCode - 角色编码
   * @returns {string} 格式化后的角色名称
   */
  formatRoleName(roleCode) {
    const roleMap = {
      'ADMIN': '系统管理员',
      'MANAGER': '管理员',
      'USER': '普通用户',
      'ROLE_ADMIN': '系统管理员',
      'ROLE_MANAGER': '管理员',
      'ROLE_USER': '普通用户'
    }
    return roleMap[roleCode] || roleCode.replace(/^ROLE_/, '')
  },

  /**
   * 格式化时间
   * @param {string} timeString - 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeString) {
    if (!timeString) return '未知'
    
    try {
      const date = new Date(timeString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (error) {
      console.error('时间格式化失败:', error)
      return '格式错误'
    }
  },

  /**
   * 计算相对时间
   * @param {string} timeString - 时间字符串
   * @returns {string} 相对时间描述
   */
  getRelativeTime(timeString) {
    if (!timeString) return '从未'
    
    try {
      const now = new Date()
      const time = new Date(timeString)
      const diff = now - time
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
      
      return time.toLocaleDateString('zh-CN')
    } catch (error) {
      console.error('相对时间计算失败:', error)
      return '时间错误'
    }
  }
}

export default profileService
