import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限检查组合式函数
 */
export function usePermission() {
  const authStore = useAuthStore()

  // 获取当前用户权限
  const userPermissions = computed(() => {
    return authStore.user?.permissions || []
  })

  // 获取当前用户角色
  const userRoles = computed(() => {
    return authStore.user?.roles || []
  })

  // 检查是否是超级管理员
  const isSuperAdmin = computed(() => {
    return userRoles.value.some(role => role.code === 'SUPER_ADMIN' || role === 'SUPER_ADMIN')
  })

  // 检查是否拥有指定权限
  const hasPermission = (permission) => {
    if (!permission) return true

    // 如果未登录，返回false
    if (!authStore.isAuthenticated) return false

    // 如果是超级管理员，拥有所有权限
    if (isSuperAdmin.value) return true

    // 检查权限数组中是否包含指定权限
    if (Array.isArray(permission)) {
      return permission.some(p => userPermissions.value.includes(p))
    }

    return userPermissions.value.includes(permission)
  }

  // 检查是否拥有所有指定权限
  const hasAllPermissions = (permissions) => {
    if (!permissions || !Array.isArray(permissions)) return true

    // 如果未登录，返回false
    if (!authStore.isAuthenticated) return false

    // 如果是超级管理员，拥有所有权限
    if (isSuperAdmin.value) return true

    return permissions.every(permission => userPermissions.value.includes(permission))
  }

  // 检查是否拥有任意一个权限
  const hasAnyPermission = (permissions) => {
    if (!permissions || !Array.isArray(permissions)) return true

    // 如果未登录，返回false
    if (!authStore.isAuthenticated) return false

    // 如果是超级管理员，拥有所有权限
    if (isSuperAdmin.value) return true

    return permissions.some(permission => userPermissions.value.includes(permission))
  }

  // 检查角色权限
  const hasRole = (role) => {
    if (!role) return true

    // 如果未登录，返回false
    if (!authStore.isAuthenticated) return false

    if (Array.isArray(role)) {
      return role.some(r => {
        return userRoles.value.some(userRole =>
          userRole.code === r || userRole === r
        )
      })
    }

    return userRoles.value.some(userRole =>
      userRole.code === role || userRole === role
    )
  }

  return {
    userPermissions,
    userRoles,
    isSuperAdmin,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRole
  }
}

export default usePermission
