package zh.backend.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.entity.User;
import zh.backend.mapper.PermissionMapper;
import zh.backend.mapper.RoleMapper;
import zh.backend.mapper.UserMapper;
import zh.backend.service.RoleService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色服务实现 - 使用 MyBatis
 */
@Service
@Transactional
public class RoleServiceImpl implements RoleService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public Role createRole(Role role) {
        if (existsByCode(role.getCode())) {
            throw new RuntimeException("角色编码已存在: " + role.getCode());
        }
        if (existsByName(role.getName())) {
            throw new RuntimeException("角色名称已存在: " + role.getName());
        }
        
        role.setCreatedAt(LocalDateTime.now());
        role.setUpdatedAt(LocalDateTime.now());
        
        roleMapper.insertRole(role);
        return role;
    }

    @Override
    public Role updateRole(Long id, Role role) {
        Role existingRole = findById(id);

        // 检查编码是否被其他角色使用
        if (!existingRole.getCode().equals(role.getCode()) &&
            existsByCode(role.getCode())) {
            throw new RuntimeException("角色编码已存在: " + role.getCode());
        }

        // 检查名称是否被其他角色使用
        if (!existingRole.getName().equals(role.getName()) &&
            existsByName(role.getName())) {
            throw new RuntimeException("角色名称已存在: " + role.getName());
        }

        existingRole.setName(role.getName());
        existingRole.setCode(role.getCode());
        existingRole.setDescription(role.getDescription());
        existingRole.setStatus(role.getStatus());
        existingRole.setSortOrder(role.getSortOrder());
        existingRole.setUpdatedAt(LocalDateTime.now());

        roleMapper.updateRole(existingRole);
        return existingRole;
    }

    @Override
    public void deleteRole(Long id) {
        Role role = findById(id);
        if (role.getIsSystem()) {
            throw new RuntimeException("系统内置角色不能删除");
        }
        
        // 清除角色的所有权限关联
        roleMapper.clearRolePermissions(id);
        // 清除用户的该角色关联
        roleMapper.clearUserRoles(id);
        // 删除角色
        roleMapper.deleteById(id);
    }

    @Override
    public Role findById(Long id) {
        Role role = roleMapper.findById(id);
        if (role == null) {
            throw new RuntimeException("角色不存在: " + id);
        }
        return role;
    }

    @Override
    public Role findByCode(String code) {
        Role role = roleMapper.findByCode(code);
        if (role == null) {
            throw new RuntimeException("角色不存在: " + code);
        }
        return role;
    }

    @Override
    public Role findByName(String name) {
        Role role = roleMapper.findByName(name);
        if (role == null) {
            throw new RuntimeException("角色不存在: " + name);
        }
        return role;
    }

    @Override
    public List<Role> findAll() {
        return roleMapper.findAll();
    }

    @Override
    public Page<Role> findByKeyword(String keyword, Role.RoleStatus status, Boolean isSystem, Pageable pageable) {
        int offset = (int) pageable.getOffset();
        int size = pageable.getPageSize();
        String sortBy = pageable.getSort().iterator().hasNext() ? 
            pageable.getSort().iterator().next().getProperty() : "id";
        String sortDir = pageable.getSort().iterator().hasNext() ? 
            pageable.getSort().iterator().next().getDirection().name().toLowerCase() : "asc";

        List<Role> roles = roleMapper.findByKeyword(keyword, offset, size, sortBy, sortDir);
        long total = roleMapper.countByKeyword(keyword);

        return new PageImpl<>(roles, pageable, total);
    }

    @Override
    public List<Role> findActiveRoles() {
        return roleMapper.findByStatus("ACTIVE");
    }

    @Override
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        
        // 清除现有权限
        roleMapper.clearRolePermissions(roleId);
        
        // 添加新权限
        if (!permissionIds.isEmpty()) {
            roleMapper.batchAssignPermissionsToRole(roleId, permissionIds);
        }
        
        log.info("为角色 {} 分配了 {} 个权限", role.getName(), permissionIds.size());
    }

    @Override
    public void removePermissionsFromRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        
        for (Long permissionId : permissionIds) {
            roleMapper.removePermissionFromRole(roleId, permissionId);
        }
        
        log.info("从角色 {} 移除了 {} 个权限", role.getName(), permissionIds.size());
    }

    @Override
    public List<Permission> getRolePermissions(Long roleId) {
        return permissionMapper.findByRoleId(roleId);
    }

    @Override
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        // 清除现有角色
        roleMapper.clearUserRoles(userId);
        
        // 添加新角色
        if (!roleIds.isEmpty()) {
            roleMapper.batchAssignRolesToUser(userId, roleIds);
        }
        
        log.info("为用户 {} 分配了 {} 个角色", user.getUsername(), roleIds.size());
    }

    @Override
    public void removeRolesFromUser(Long userId, List<Long> roleIds) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        for (Long roleId : roleIds) {
            roleMapper.removeRoleFromUser(userId, roleId);
        }
        
        log.info("从用户 {} 移除了 {} 个角色", user.getUsername(), roleIds.size());
    }

    @Override
    public List<Role> getUserRoles(Long userId) {
        return roleMapper.findByUserId(userId);
    }

    @Override
    public List<User> getRoleUsers(Long roleId) {
        return userMapper.findByRoleCode(findById(roleId).getCode());
    }

    @Override
    public boolean existsByCode(String code) {
        return roleMapper.existsByCode(code);
    }

    @Override
    public boolean existsByName(String name) {
        return roleMapper.existsByName(name);
    }

    @Override
    public List<Role> findByStatus(Role.RoleStatus status) {
        return roleMapper.findByStatus(status.name());
    }

    @Override
    public List<Role> findSystemRoles() {
        return roleMapper.findByIsSystemTrue();
    }

    @Override
    public List<Role> findCustomRoles() {
        return roleMapper.findByIsSystemFalse();
    }

    @Override
    public long countByStatus(Role.RoleStatus status) {
        return roleMapper.countByStatus(status.name());
    }

    @Override
    public void deleteRoles(List<Long> ids) {
        // 检查是否有系统角色
        for (Long id : ids) {
            Role role = findById(id);
            if (role.getIsSystem()) {
                throw new RuntimeException("系统内置角色不能删除: " + role.getName());
            }
        }
        
        // 清除所有关联
        for (Long id : ids) {
            roleMapper.clearRolePermissions(id);
            roleMapper.clearUserRoles(id);
        }
        
        // 删除角色
        roleMapper.deleteByIds(ids);
    }
}
