import axios from 'axios';
import { useAuthStore } from '@/stores/auth'; // Import Pinia store

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080', // Use env var or default
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // Get token from Pinia store
    const authStore = useAuthStore(); // Instantiate store *inside* interceptor
    const token = authStore.token;
    
    if (token) {
      // Add token to request header
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      const authStore = useAuthStore();
      // Only logout if there was a token initially, avoids logout loops on public pages if API returns 401 wrongly
      if (authStore.token) {
         console.warn('API request Unauthorized (401). Logging out.');
         authStore.logout(); // Use store's logout action
         // Redirect to login page (or let router handle it if configured)
         window.location.href = '/login';
         // Or: router.push('/login'); if router instance is available here
      }
    }
    return Promise.reject(error);
  }
);

export default api;
