package zh.backend.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.dto.ApiResponse;
import zh.backend.dto.PermissionDTO;
import zh.backend.dto.PermissionTreeDTO;
import zh.backend.dto.UserPermissionDTO;
import zh.backend.entity.Permission;
import zh.backend.entity.User;
import zh.backend.service.PermissionService;
import zh.backend.service.UserService;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 权限管理控制器
 * 提供权限的CRUD操作、权限树构建、用户权限管理等功能
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024
 */
@Slf4j
@RestController
@RequestMapping("/api/permissions")
@RequirePermission("PERMISSION_MANAGEMENT")
public class PermissionController {

    private final PermissionService permissionService;
    private final UserService userService;

    @Autowired
    public PermissionController(PermissionService permissionService, UserService userService) {
        this.permissionService = permissionService;
        this.userService = userService;
    }

    // ==================== 权限基础CRUD操作 ====================

    /**
     * 获取权限列表（分页）
     * 支持关键词搜索、资源类型过滤、状态过滤等
     *
     * @param keyword 搜索关键词
     * @param page 页码（从0开始）
     * @param size 每页大小（1-100）
     * @param sortBy 排序字段
     * @param sortDir 排序方向（asc/desc）
     * @param resourceType 资源类型过滤
     * @param status 状态过滤
     * @param isSystem 是否系统权限过滤
     * @return 分页权限列表
     */
    @GetMapping
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Page<PermissionDTO>>> getPermissions(
            @RequestParam(defaultValue = "") String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) Permission.ResourceType resourceType,
            @RequestParam(required = false) Permission.PermissionStatus status,
            @RequestParam(required = false) Boolean isSystem) {

        log.info("获取权限列表 - 关键词: {}, 页码: {}, 大小: {}, 排序: {} {}",
                keyword, page, size, sortBy, sortDir);

        try {
            // 参数验证
            if (page < 0) {
                log.warn("无效的页码参数: {}", page);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("页码不能小于0"));
            }
            if (size <= 0 || size > 100) {
                log.warn("无效的页面大小参数: {}", size);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("每页大小必须在1-100之间"));
            }

            // 构建排序
            Sort sort = buildSort(sortBy, sortDir);
            Pageable pageable = PageRequest.of(page, size, sort);

            // 查询权限
            Page<Permission> permissions = permissionService.findByKeyword(keyword, pageable);

            // 应用额外过滤条件
            if (resourceType != null || status != null || isSystem != null) {
                permissions = applyFilters(permissions, pageable, resourceType, status, isSystem);
            }

            // 转换为DTO
            Page<PermissionDTO> permissionDTOs = permissions.map(PermissionDTO::new);

            log.info("成功获取权限列表，共 {} 条记录", permissionDTOs.getTotalElements());
            return ResponseEntity.ok(ApiResponse.success("获取权限列表成功", permissionDTOs));

        } catch (Exception e) {
            log.error("获取权限列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取权限树
     * 支持按资源类型过滤和包含/排除非活跃权限
     *
     * @param resourceType 资源类型过滤（可选）
     * @param includeInactive 是否包含非活跃权限
     * @return 权限树结构
     */
    @GetMapping("/tree")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<PermissionTreeDTO>>> getPermissionTree(
            @RequestParam(required = false) Permission.ResourceType resourceType,
            @RequestParam(defaultValue = "false") boolean includeInactive) {

        log.info("获取权限树 - 资源类型: {}, 包含非活跃: {}", resourceType, includeInactive);

        try {
            List<Permission> permissions = permissionService.buildPermissionTree();

            if (permissions.isEmpty()) {
                log.warn("未找到任何权限数据");
                return ResponseEntity.ok(ApiResponse.success("获取权限树成功", new ArrayList<>()));
            }

            // 转换为树形DTO结构
            List<PermissionTreeDTO> tree = buildPermissionTreeDTO(permissions, resourceType, includeInactive);

            log.info("成功构建权限树，根节点数量: {}", tree.size());
            return ResponseEntity.ok(ApiResponse.success("获取权限树成功", tree));

        } catch (Exception e) {
            log.error("获取权限树失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限树失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取权限详情
     *
     * @param id 权限ID
     * @return 权限详情
     */
    @GetMapping("/{id}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<PermissionDTO>> getPermission(@PathVariable Long id) {
        log.info("获取权限详情 - ID: {}", id);

        try {
            if (id == null || id <= 0) {
                log.warn("无效的权限ID: {}", id);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限ID不能为空或小于等于0"));
            }

            Permission permission = permissionService.findById(id);
            if (permission == null) {
                log.warn("权限不存在 - ID: {}", id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("权限不存在"));
            }

            PermissionDTO dto = new PermissionDTO(permission);
            log.info("成功获取权限详情 - ID: {}, 名称: {}", id, permission.getName());
            return ResponseEntity.ok(ApiResponse.success("获取权限详情成功", dto));

        } catch (RuntimeException e) {
            log.error("权限不存在 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.notFound("权限不存在: " + e.getMessage()));
        } catch (Exception e) {
            log.error("获取权限详情失败 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限详情失败: " + e.getMessage()));
        }
    }

    /**
     * 创建权限
     *
     * @param permissionDTO 权限数据传输对象
     * @return 创建的权限信息
     */
    @PostMapping
    @RequirePermission("PERMISSION_CREATE")
    public ResponseEntity<ApiResponse<PermissionDTO>> createPermission(@Valid @RequestBody PermissionDTO permissionDTO) {
        log.info("创建权限 - 名称: {}, 编码: {}", permissionDTO.getName(), permissionDTO.getCode());

        try {
            // 参数验证
            if (permissionDTO == null) {
                log.warn("权限数据不能为空");
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限数据不能为空"));
            }

            if (!StringUtils.hasText(permissionDTO.getCode())) {
                log.warn("权限编码不能为空");
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限编码不能为空"));
            }

            // 检查权限编码是否已存在
            if (permissionService.existsByCode(permissionDTO.getCode())) {
                log.warn("权限编码已存在: {}", permissionDTO.getCode());
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("权限编码已存在"));
            }

            // 获取当前用户信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userService.findByUsername(auth.getName());

            Permission permission = permissionDTO.toEntity();
            permission.setCreatedBy(currentUser.getId());

            Permission createdPermission = permissionService.createPermission(permission);
            PermissionDTO resultDTO = new PermissionDTO(createdPermission);

            log.info("成功创建权限 - ID: {}, 名称: {}", createdPermission.getId(), createdPermission.getName());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("权限创建成功", resultDTO));

        } catch (RuntimeException e) {
            log.error("权限创建失败 - 业务异常", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.badRequest("权限创建失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("权限创建失败 - 系统异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限创建失败: " + e.getMessage()));
        }
    }

    /**
     * 更新权限
     *
     * @param id 权限ID
     * @param permissionDTO 权限数据传输对象
     * @return 更新后的权限信息
     */
    @PutMapping("/{id}")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<PermissionDTO>> updatePermission(
            @PathVariable Long id,
            @Valid @RequestBody PermissionDTO permissionDTO) {
        log.info("更新权限 - ID: {}, 名称: {}", id, permissionDTO.getName());

        try {
            // 参数验证
            if (!isValidPermissionId(id)) {
                log.warn("无效的权限ID: {}", id);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限ID不能为空或小于等于0"));
            }

            if (permissionDTO == null) {
                log.warn("权限数据不能为空");
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限数据不能为空"));
            }

            // 检查权限是否存在
            Permission existingPermission = permissionService.findById(id);
            if (existingPermission == null) {
                log.warn("权限不存在 - ID: {}", id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("权限不存在"));
            }

            // 检查是否为系统权限
            if (existingPermission.getIsSystem()) {
                log.warn("系统权限不允许修改 - ID: {}", id);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.forbidden("系统权限不允许修改"));
            }

            // 检查权限编码是否与其他权限冲突
            if (!existingPermission.getCode().equals(permissionDTO.getCode()) &&
                permissionService.existsByCode(permissionDTO.getCode())) {
                log.warn("权限编码已存在: {}", permissionDTO.getCode());
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("权限编码已存在"));
            }

            // 获取当前用户信息
            User currentUser = getCurrentUser();

            Permission permission = permissionDTO.toEntity();
            permission.setUpdatedBy(currentUser.getId());

            Permission updatedPermission = permissionService.updatePermission(id, permission);
            PermissionDTO resultDTO = new PermissionDTO(updatedPermission);

            log.info("成功更新权限 - ID: {}, 名称: {}", id, updatedPermission.getName());
            return ResponseEntity.ok(ApiResponse.success("权限更新成功", resultDTO));

        } catch (RuntimeException e) {
            log.error("权限更新失败 - 业务异常 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.badRequest("权限更新失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("权限更新失败 - 系统异常 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限更新失败: " + e.getMessage()));
        }
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<ApiResponse<Void>> deletePermission(@PathVariable Long id) {
        log.info("删除权限 - ID: {}", id);

        try {
            // 参数验证
            if (!isValidPermissionId(id)) {
                log.warn("无效的权限ID: {}", id);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限ID不能为空或小于等于0"));
            }

            Permission permission = permissionService.findById(id);
            if (permission == null) {
                log.warn("权限不存在 - ID: {}", id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("权限不存在"));
            }

            // 检查是否为系统权限
            if (permission.getIsSystem()) {
                log.warn("系统权限不允许删除 - ID: {}", id);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.forbidden("系统权限不允许删除"));
            }

            // 检查是否有子权限
            List<Permission> children = permissionService.findByParentId(id);
            if (!children.isEmpty()) {
                log.warn("权限存在子权限，不允许删除 - ID: {}, 子权限数量: {}", id, children.size());
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("权限存在子权限，请先删除子权限"));
            }

            permissionService.deletePermission(id);
            log.info("成功删除权限 - ID: {}, 名称: {}", id, permission.getName());
            return ResponseEntity.ok(ApiResponse.<Void>success("权限删除成功", null));

        } catch (RuntimeException e) {
            log.error("权限删除失败 - 业务异常 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.badRequest("权限删除失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("权限删除失败 - 系统异常 - ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限删除失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除权限
     */
    @DeleteMapping("/batch")
    @RequirePermission("PERMISSION_DELETE")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deletePermissions(@RequestBody List<Long> ids) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Long> successIds = new ArrayList<>();
            List<String> failedReasons = new ArrayList<>();

            for (Long id : ids) {
                try {
                    Permission permission = permissionService.findById(id);
                    if (permission.getIsSystem()) {
                        failedReasons.add("权限ID " + id + ": 系统权限不允许删除");
                        continue;
                    }

                    permissionService.deletePermission(id);
                    successIds.add(id);
                } catch (Exception e) {
                    failedReasons.add("权限ID " + id + ": " + e.getMessage());
                }
            }

            result.put("successCount", successIds.size());
            result.put("failedCount", failedReasons.size());
            result.put("successIds", successIds);
            result.put("failedReasons", failedReasons);

            if (failedReasons.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.success("批量删除权限成功", result));
            } else {
                return ResponseEntity.ok(ApiResponse.success("批量删除权限部分成功", result));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("批量删除权限失败: " + e.getMessage()));
        }
    }

    /**
     * 根据资源类型获取权限
     */
    @GetMapping("/by-resource-type")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getPermissionsByResourceType(
            @RequestParam Permission.ResourceType resourceType) {
        try {
            List<Permission> permissions = permissionService.findByResourceType(resourceType);
            List<PermissionDTO> permissionDTOs = permissions.stream()
                .map(PermissionDTO::new)
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取权限列表成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据父权限ID获取子权限
     */
    @GetMapping("/children/{parentId}")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<PermissionDTO>>> getChildPermissions(@PathVariable Long parentId) {
        try {
            List<Permission> children = permissionService.findByParentId(parentId);
            List<PermissionDTO> childrenDTOs = children.stream()
                .map(PermissionDTO::new)
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取子权限列表成功", childrenDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取子权限列表失败: " + e.getMessage()));
        }
    }

    /**
     * 检查权限编码是否存在
     */
    @GetMapping("/exists")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkPermissionExists(@RequestParam String code) {
        try {
            boolean exists = permissionService.existsByCode(code);
            return ResponseEntity.ok(ApiResponse.success("检查权限编码成功", exists));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("检查权限编码失败: " + e.getMessage()));
        }
    }

    // ==================== 权限管理操作 ====================

    /**
     * 清除权限缓存
     *
     * @return 清除结果
     */
    @PostMapping("/clear-cache")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> clearPermissionCache() {
        log.info("清除权限缓存");

        try {
            permissionService.clearPermissionCache();
            log.info("成功清除权限缓存");
            return ResponseEntity.ok(ApiResponse.<Void>success("权限缓存清除成功", null));

        } catch (Exception e) {
            log.error("权限缓存清除失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限缓存清除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取权限统计信息
     *
     * @return 权限统计数据
     */
    @GetMapping("/statistics")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPermissionStatistics() {
        log.info("获取权限统计信息");

        try {
            List<Permission> allPermissions = permissionService.findAll();

            if (allPermissions.isEmpty()) {
                log.warn("未找到任何权限数据");
                Map<String, Object> emptyStats = new HashMap<>();
                emptyStats.put("total", 0);
                emptyStats.put("active", 0);
                emptyStats.put("inactive", 0);
                emptyStats.put("system", 0);
                emptyStats.put("custom", 0);
                emptyStats.put("byResourceType", new HashMap<>());
                emptyStats.put("byLevel", new HashMap<>());
                return ResponseEntity.ok(ApiResponse.success("获取权限统计信息成功", emptyStats));
            }

            long total = allPermissions.size();
            long active = allPermissions.stream()
                .filter(p -> p.getStatus() == Permission.PermissionStatus.ACTIVE)
                .count();
            long inactive = total - active;
            long system = allPermissions.stream()
                .filter(Permission::getIsSystem)
                .count();
            long custom = total - system;

            // 按资源类型统计
            Map<Permission.ResourceType, Long> byResourceType = allPermissions.stream()
                .collect(Collectors.groupingBy(Permission::getResourceType, Collectors.counting()));

            // 按层级统计
            Map<Integer, Long> byLevel = allPermissions.stream()
                .collect(Collectors.groupingBy(Permission::getLevel, Collectors.counting()));

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("total", total);
            statistics.put("active", active);
            statistics.put("inactive", inactive);
            statistics.put("system", system);
            statistics.put("custom", custom);
            statistics.put("byResourceType", byResourceType);
            statistics.put("byLevel", byLevel);

            log.info("成功获取权限统计信息 - 总数: {}, 活跃: {}, 系统: {}", total, active, system);
            return ResponseEntity.ok(ApiResponse.success("获取权限统计信息成功", statistics));

        } catch (Exception e) {
            log.error("获取权限统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取权限统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的直接权限
     */
    @GetMapping("/user/{userId}/direct")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<UserPermissionDTO>>> getUserDirectPermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserDirectPermissions(userId);
            List<UserPermissionDTO> permissionDTOs = permissions.stream()
                .map(permission -> {
                    UserPermissionDTO dto = new UserPermissionDTO();
                    dto.setUserId(userId);
                    dto.setPermissionId(permission.getId());
                    dto.setPermissionName(permission.getName());
                    dto.setPermissionCode(permission.getCode());
                    dto.setSource("DIRECT");
                    return dto;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取用户直接权限成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户直接权限失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的角色权限
     */
    @GetMapping("/user/{userId}/role")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<List<UserPermissionDTO>>> getUserRolePermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserRolePermissions(userId);
            List<UserPermissionDTO> permissionDTOs = permissions.stream()
                .map(permission -> {
                    UserPermissionDTO dto = new UserPermissionDTO();
                    dto.setUserId(userId);
                    dto.setPermissionId(permission.getId());
                    dto.setPermissionName(permission.getName());
                    dto.setPermissionCode(permission.getCode());
                    dto.setSource("ROLE");
                    return dto;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(ApiResponse.success("获取用户角色权限成功", permissionDTOs));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户角色权限失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的所有权限（包括直接权限和角色权限）
     */
    @GetMapping("/user/{userId}/all")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Set<String>>> getAllUserPermissions(@PathVariable Long userId) {
        try {
            Set<String> permissions = permissionService.getUserPermissions(userId);
            return ResponseEntity.ok(ApiResponse.success("获取用户所有权限成功", permissions));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户所有权限失败: " + e.getMessage()));
        }
    }

    // ==================== 用户权限管理 ====================

    /**
     * 为用户分配直接权限
     *
     * @param userId 用户ID
     * @param permissionId 权限ID
     * @param expiresAt 过期时间（可选）
     * @return 分配结果
     */
    @PostMapping("/user/{userId}/grant")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> grantPermissionToUser(
            @PathVariable Long userId,
            @RequestParam Long permissionId,
            @RequestParam(required = false) String expiresAt) {
        log.info("为用户分配权限 - 用户ID: {}, 权限ID: {}", userId, permissionId);

        try {
            // 参数验证
            if (!isValidUserId(userId)) {
                log.warn("无效的用户ID: {}", userId);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("用户ID不能为空或小于等于0"));
            }

            if (!isValidPermissionId(permissionId)) {
                log.warn("无效的权限ID: {}", permissionId);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限ID不能为空或小于等于0"));
            }

            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                log.warn("用户不存在 - ID: {}", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("用户不存在"));
            }

            // 检查权限是否存在
            Permission permission = permissionService.findById(permissionId);
            if (permission == null) {
                log.warn("权限不存在 - ID: {}", permissionId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("权限不存在"));
            }

            // 检查用户是否已经拥有该权限
            if (permissionService.hasPermission(userId, permission.getCode())) {
                log.warn("用户已拥有该权限 - 用户ID: {}, 权限编码: {}", userId, permission.getCode());
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error("用户已拥有该权限"));
            }

            User currentUser = getCurrentUser();
            permissionService.grantPermissionToUser(userId, permissionId, currentUser.getId());

            log.info("成功为用户分配权限 - 用户: {}, 权限: {}", user.getUsername(), permission.getName());
            return ResponseEntity.ok(ApiResponse.<Void>success("权限分配成功", null));

        } catch (RuntimeException e) {
            log.error("权限分配失败 - 业务异常 - 用户ID: {}, 权限ID: {}", userId, permissionId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.badRequest("权限分配失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("权限分配失败 - 系统异常 - 用户ID: {}, 权限ID: {}", userId, permissionId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限分配失败: " + e.getMessage()));
        }
    }

    /**
     * 撤销用户的直接权限
     *
     * @param userId 用户ID
     * @param permissionId 权限ID
     * @return 撤销结果
     */
    @DeleteMapping("/user/{userId}/revoke")
    @RequirePermission("PERMISSION_UPDATE")
    public ResponseEntity<ApiResponse<Void>> revokePermissionFromUser(
            @PathVariable Long userId,
            @RequestParam Long permissionId) {
        log.info("撤销用户权限 - 用户ID: {}, 权限ID: {}", userId, permissionId);

        try {
            // 参数验证
            if (!isValidUserId(userId)) {
                log.warn("无效的用户ID: {}", userId);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("用户ID不能为空或小于等于0"));
            }

            if (!isValidPermissionId(permissionId)) {
                log.warn("无效的权限ID: {}", permissionId);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限ID不能为空或小于等于0"));
            }

            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                log.warn("用户不存在 - ID: {}", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("用户不存在"));
            }

            // 检查权限是否存在
            Permission permission = permissionService.findById(permissionId);
            if (permission == null) {
                log.warn("权限不存在 - ID: {}", permissionId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("权限不存在"));
            }

            permissionService.revokePermissionFromUser(userId, permissionId);
            log.info("成功撤销用户权限 - 用户: {}, 权限: {}", user.getUsername(), permission.getName());
            return ResponseEntity.ok(ApiResponse.<Void>success("权限撤销成功", null));

        } catch (RuntimeException e) {
            log.error("权限撤销失败 - 业务异常 - 用户ID: {}, 权限ID: {}", userId, permissionId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.badRequest("权限撤销失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("权限撤销失败 - 系统异常 - 用户ID: {}, 权限ID: {}", userId, permissionId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限撤销失败: " + e.getMessage()));
        }
    }

    /**
     * 检查用户是否拥有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 权限检查结果
     */
    @GetMapping("/user/{userId}/check")
    @RequirePermission("PERMISSION_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkUserPermission(
            @PathVariable Long userId,
            @RequestParam String permissionCode) {
        log.info("检查用户权限 - 用户ID: {}, 权限编码: {}", userId, permissionCode);

        try {
            // 参数验证
            if (!isValidUserId(userId)) {
                log.warn("无效的用户ID: {}", userId);
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("用户ID不能为空或小于等于0"));
            }

            if (!StringUtils.hasText(permissionCode)) {
                log.warn("权限编码不能为空");
                return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("权限编码不能为空"));
            }

            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                log.warn("用户不存在 - ID: {}", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.notFound("用户不存在"));
            }

            boolean hasPermission = permissionService.hasPermission(userId, permissionCode);
            log.info("用户权限检查结果 - 用户: {}, 权限: {}, 结果: {}",
                    user.getUsername(), permissionCode, hasPermission);
            return ResponseEntity.ok(ApiResponse.success("权限检查成功", hasPermission));

        } catch (Exception e) {
            log.error("权限检查失败 - 用户ID: {}, 权限编码: {}", userId, permissionCode, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("权限检查失败: " + e.getMessage()));
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 构建排序对象
     *
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return Sort对象
     */
    private Sort buildSort(String sortBy, String sortDir) {
        try {
            Sort.Direction direction = Sort.Direction.fromString(sortDir);
            return Sort.by(direction, sortBy);
        } catch (IllegalArgumentException e) {
            log.warn("无效的排序参数: {} {}, 使用默认排序", sortBy, sortDir);
            return Sort.by(Sort.Direction.ASC, "id");
        }
    }

    /**
     * 应用过滤条件
     *
     * @param permissions 原始分页数据
     * @param pageable 分页参数
     * @param resourceType 资源类型过滤
     * @param status 状态过滤
     * @param isSystem 是否系统权限过滤
     * @return 过滤后的分页数据
     */
    private Page<Permission> applyFilters(Page<Permission> permissions, Pageable pageable,
                                        Permission.ResourceType resourceType,
                                        Permission.PermissionStatus status,
                                        Boolean isSystem) {
        List<Permission> filteredList = permissions.getContent().stream()
            .filter(permission -> resourceType == null || permission.getResourceType() == resourceType)
            .filter(permission -> status == null || permission.getStatus() == status)
            .filter(permission -> isSystem == null || permission.getIsSystem().equals(isSystem))
            .collect(Collectors.toList());

        return new org.springframework.data.domain.PageImpl<>(
            filteredList, pageable, filteredList.size());
    }

    /**
     * 构建权限树DTO的辅助方法
     *
     * @param permissions 权限列表
     * @param resourceType 资源类型过滤
     * @param includeInactive 是否包含非活跃权限
     * @return 权限树结构
     */
    private List<PermissionTreeDTO> buildPermissionTreeDTO(List<Permission> permissions,
                                                          Permission.ResourceType resourceType,
                                                          boolean includeInactive) {
        // 过滤权限
        List<Permission> filteredPermissions = permissions.stream()
            .filter(p -> resourceType == null || p.getResourceType() == resourceType)
            .filter(p -> includeInactive || p.getStatus() == Permission.PermissionStatus.ACTIVE)
            .collect(Collectors.toList());

        // 构建树形结构
        Map<Long, PermissionTreeDTO> dtoMap = new HashMap<>();
        List<PermissionTreeDTO> rootNodes = new ArrayList<>();

        // 创建所有DTO节点
        for (Permission permission : filteredPermissions) {
            PermissionTreeDTO dto = new PermissionTreeDTO(permission);
            dtoMap.put(permission.getId(), dto);
        }

        // 构建父子关系
        for (Permission permission : filteredPermissions) {
            PermissionTreeDTO dto = dtoMap.get(permission.getId());
            if (permission.getParentId() == null) {
                rootNodes.add(dto);
            } else {
                PermissionTreeDTO parent = dtoMap.get(permission.getParentId());
                if (parent != null) {
                    parent.addChild(dto);
                }
            }
        }

        return rootNodes;
    }

    /**
     * 获取当前登录用户
     *
     * @return 当前用户
     */
    private User getCurrentUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return userService.findByUsername(auth.getName());
    }

    /**
     * 验证用户ID是否有效
     *
     * @param userId 用户ID
     * @return 是否有效
     */
    private boolean isValidUserId(Long userId) {
        return userId != null && userId > 0;
    }

    /**
     * 验证权限ID是否有效
     *
     * @param permissionId 权限ID
     * @return 是否有效
     */
    private boolean isValidPermissionId(Long permissionId) {
        return permissionId != null && permissionId > 0;
    }
}
