package zh.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import zh.backend.entity.User;
import zh.backend.entity.Role;
import zh.backend.service.UserService;
import zh.backend.service.PermissionService;
import zh.backend.util.JwtUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Transactional(readOnly = true)
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        // 支持username或usernameOrEmail字段
        String username = loginRequest.get("username");
        if (username == null) {
            username = loginRequest.get("usernameOrEmail");
        }
        String password = loginRequest.get("password");

        System.out.println("登录请求 - 用户名: " + username + ", 密码长度: " + (password != null ? password.length() : "null"));
        System.out.println("请求体: " + loginRequest);

        try {
            // 从数据库查找用户（支持用户名或邮箱，包含角色和权限）
            User user = null;
            try {
                // 使用带有角色和权限的查询方法
                user = userService.findByUsernameWithRolesAndPermissions(username);
                if (user == null) {
                    // 如果通过用户名找不到，尝试通过邮箱查找
                    user = userService.findByUsernameOrEmail(username);
                }
            } catch (Exception e) {
                // 如果找不到用户，返回错误
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(response);
            }
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证密码
            if (!passwordEncoder.matches(password, user.getPassword())) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "密码错误");
                return ResponseEntity.badRequest().body(response);
            }

            // 检查用户状态
            if (user.getStatus() != User.UserStatus.ACTIVE) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "用户账户已被禁用");
                return ResponseEntity.badRequest().body(response);
            }

            // 生成JWT token
            String token = jwtUtil.generateToken(user.getId(), username);

            // 获取用户权限
            Set<String> permissions = permissionService.getUserPermissions(user);

            // 获取用户角色
            Set<String> roleCodes = user.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", token);
            response.put("user", Map.of(
                "id", user.getId(),
                "username", user.getUsername(),
                "email", user.getEmail(),
                "realName", user.getRealName(),
                "roles", roleCodes,
                "permissions", permissions
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "登录失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody Map<String, String> registerRequest) {
        String username = registerRequest.get("username");
        String password = registerRequest.get("password");
        String email = registerRequest.get("email");

        // 简化版注册 - 实际项目中应该保存到数据库
        String encodedPassword = passwordEncoder.encode(password);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "注册成功");
        response.put("user", Map.of(
            "username", username,
            "email", email
        ));

        return ResponseEntity.ok(response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<Map<String, Object>> getCurrentUser() {
        try {
            // 简化版：直接获取admin用户信息
            // 实际项目中应该从JWT token或SecurityContext获取当前用户ID
            User user = userService.findByUsername("admin");
            if (user == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(response);
            }

            // 获取用户权限
            Set<String> permissions = permissionService.getUserPermissions(user);

            // 获取用户角色
            Set<String> roleCodes = user.getRoles().stream()
                .map(Role::getCode)
                .collect(Collectors.toSet());

            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("email", user.getEmail());
            userInfo.put("realName", user.getRealName());
            userInfo.put("roles", roleCodes);
            userInfo.put("permissions", permissions);

            return ResponseEntity.ok(userInfo);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取用户信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "登出成功");

        return ResponseEntity.ok(response);
    }
}
