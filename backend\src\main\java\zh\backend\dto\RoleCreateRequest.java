package zh.backend.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import zh.backend.entity.Role;

import java.util.List;

/**
 * 角色创建请求DTO
 */
public class RoleCreateRequest {

    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    private String name;

    @NotBlank(message = "角色编码不能为空")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "角色编码必须以大写字母开头，只能包含大写字母、数字和下划线")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    private String code;

    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    private Role.RoleStatus status = Role.RoleStatus.ACTIVE;

    @Min(value = 0, message = "排序值不能小于0")
    @Max(value = 999, message = "排序值不能大于999")
    private Integer sortOrder = 0;

    private List<Long> permissionIds;

    private String templateCode; // 角色模板编码，用于快速创建

    // 构造函数
    public RoleCreateRequest() {}

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Role.RoleStatus getStatus() {
        return status;
    }

    public void setStatus(Role.RoleStatus status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<Long> getPermissionIds() {
        return permissionIds;
    }

    public void setPermissionIds(List<Long> permissionIds) {
        this.permissionIds = permissionIds;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    // 转换为实体
    public Role toEntity() {
        Role role = new Role();
        role.setName(this.name);
        role.setCode(this.code);
        role.setDescription(this.description);
        role.setStatus(this.status);
        role.setSortOrder(this.sortOrder);
        role.setIsSystem(false); // 创建的角色默认为非系统角色
        return role;
    }
}
