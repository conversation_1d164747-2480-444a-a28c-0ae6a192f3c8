# 动态权限管理系统后端

基于Spring Boot 3.3.5 + Java 17的动态权限管理系统后端服务。

## 🚀 快速启动

### 前置要求

- Java 17+
- Maven 3.6+
- MySQL 8.0+ (或使用H2内存数据库)

### 启动步骤

1. **配置数据库**
   
   编辑 `src/main/resources/application.yml` 文件，配置数据库连接：
   
   ```yaml
   spring:
     datasource:
       url: *******************************************************************************************************************
       username: root
       password: 123456
   ```

2. **编译项目**
   
   ```bash
   mvn clean compile
   ```

3. **启动应用**
   
   ```bash
   mvn spring-boot:run
   ```
   
   或者在Windows上运行：
   ```bash
   start.bat
   ```

4. **验证启动**
   
   访问健康检查接口：
   ```
   GET http://localhost:8080/api/health
   ```

## 🔧 API接口

### 健康检查
- `GET /api/health` - 系统健康状态
- `GET /api/health/info` - 系统信息

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 测试接口
- `GET /api/test/public` - 公开接口（无需认证）
- `GET /api/test/permission` - 需要权限的接口
- `GET /api/test/role` - 需要角色的接口
- `GET /api/test/anonymous` - 允许匿名访问的接口

### 权限管理接口
- `GET /api/permissions` - 获取权限列表
- `POST /api/permissions` - 创建权限
- `PUT /api/permissions/{id}` - 更新权限
- `DELETE /api/permissions/{id}` - 删除权限

### 角色管理接口
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/{id}` - 更新角色
- `DELETE /api/roles/{id}` - 删除角色

### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

## 🔐 默认账户

- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员

## 📊 数据库表结构

- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `user_roles` - 用户角色关联表
- `role_permissions` - 角色权限关联表
- `user_permissions` - 用户直接权限表
- `permission_policies` - 权限策略表
- `permission_logs` - 权限操作日志表

## 🛠️ 开发配置

### 数据库配置

**MySQL (生产环境)**
```yaml
spring:
  datasource:
    url: *********************************************
    username: root
    password: your_password
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
```

**H2 (开发环境)**
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
```

### 日志配置

```yaml
logging:
  level:
    zh.backend: DEBUG
    org.springframework.security: DEBUG
```

## 🔍 故障排除

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置
   - 确认数据库用户权限

2. **编译错误**
   - 确认Java版本为17+
   - 检查Maven配置
   - 清理并重新编译：`mvn clean compile`

3. **启动失败**
   - 检查端口8080是否被占用
   - 查看控制台错误日志
   - 验证数据库连接

## 📝 注意事项

- 首次启动会自动创建数据库表结构
- 系统会自动初始化默认用户和权限数据
- 开发环境使用`create-drop`模式，重启会重置数据
- 生产环境建议使用Flyway进行数据库版本管理
