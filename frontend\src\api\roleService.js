import api from './axios'

const API_URL = '/api/roles'

/**
 * 角色管理API服务
 */
export const roleService = {
  /**
   * 获取角色列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getRoles(params = {}) {
    const response = await api.get(API_URL, { params })
    return response.data
  },

  /**
   * 获取角色列表（增强版，支持更多过滤条件）
   * @param {Object} filters - 过滤条件
   * @returns {Promise}
   */
  async getRolesWithFilters(filters = {}) {
    const params = {
      keyword: filters.keyword || '',
      status: filters.status || null,
      isSystem: filters.isSystem !== undefined ? filters.isSystem : null,
      page: filters.page || 0,
      size: filters.size || 10,
      sortBy: filters.sortBy || 'sortOrder',
      sortDir: filters.sortDir || 'asc'
    }
    
    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await api.get(API_URL, { params })
    return response.data
  },

  /**
   * 根据ID获取角色详情
   * @param {number} id - 角色ID
   * @returns {Promise}
   */
  async getRoleById(id) {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 创建角色
   * @param {Object} roleData - 角色数据
   * @returns {Promise}
   */
  async createRole(roleData) {
    const response = await api.post(API_URL, roleData)
    return response.data
  },

  /**
   * 更新角色
   * @param {number} id - 角色ID
   * @param {Object} roleData - 角色数据
   * @returns {Promise}
   */
  async updateRole(id, roleData) {
    const response = await api.put(`${API_URL}/${id}`, roleData)
    return response.data
  },

  /**
   * 删除角色
   * @param {number} id - 角色ID
   * @returns {Promise}
   */
  async deleteRole(id) {
    const response = await api.delete(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 批量删除角色
   * @param {Array} ids - 角色ID数组
   * @returns {Promise}
   */
  async deleteRoles(ids) {
    const response = await api.delete(`${API_URL}/batch`, { data: ids })
    return response.data
  },

  /**
   * 获取角色权限
   * @param {number} roleId - 角色ID
   * @returns {Promise}
   */
  async getRolePermissions(roleId) {
    const response = await api.get(`${API_URL}/${roleId}/permissions`)
    return response.data
  },

  /**
   * 分配角色权限（新的统一接口）
   * @param {Object} assignData - 分配数据
   * @returns {Promise}
   */
  async assignRolePermissions(assignData) {
    const response = await api.post(`${API_URL}/permissions`, assignData)
    return response.data
  },

  /**
   * 为角色分配权限（兼容旧接口）
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async assignPermissionsToRole(roleId, permissionIds) {
    return this.assignRolePermissions({
      roleId,
      permissionIds,
      replaceAll: true
    })
  },

  /**
   * 为角色分配权限（别名方法，兼容新的调用方式）
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async assignPermissions(roleId, permissionIds) {
    return this.assignPermissionsToRole(roleId, permissionIds)
  },

  /**
   * 从角色中移除权限
   * @param {number} roleId - 角色ID
   * @param {Array} permissionIds - 权限ID数组
   * @returns {Promise}
   */
  async removePermissions(roleId, permissionIds) {
    const response = await api.delete(`${API_URL}/${roleId}/permissions`, { data: permissionIds })
    return response.data
  },

  /**
   * 复制角色权限
   * @param {number} sourceRoleId - 源角色ID
   * @param {number} targetRoleId - 目标角色ID
   * @returns {Promise}
   */
  async copyRolePermissions(sourceRoleId, targetRoleId) {
    const response = await api.post(`${API_URL}/${sourceRoleId}/copy-to/${targetRoleId}`)
    return response.data
  },

  /**
   * 获取角色用户
   * @param {number} roleId - 角色ID
   * @returns {Promise}
   */
  async getRoleUsers(roleId) {
    const response = await api.get(`${API_URL}/${roleId}/users`)
    return response.data
  },

  /**
   * 为角色分配用户
   * @param {number} roleId - 角色ID
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise}
   */
  async assignUsersToRole(roleId, userIds) {
    const response = await api.post(`${API_URL}/${roleId}/users`, { userIds })
    return response.data
  },

  /**
   * 从角色中移除用户
   * @param {number} roleId - 角色ID
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise}
   */
  async removeUsersFromRole(roleId, userIds) {
    const response = await api.delete(`${API_URL}/${roleId}/users`, { data: { userIds } })
    return response.data
  },

  /**
   * 获取角色统计信息
   * @returns {Promise}
   */
  async getRoleStats() {
    const response = await api.get(`${API_URL}/statistics`)
    return response.data
  },

  /**
   * 检查角色编码是否存在
   * @param {string} code - 角色编码
   * @param {number} excludeId - 排除的角色ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkRoleCode(code, excludeId = null) {
    const params = { code }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/exists/code`, { params })
    return response.data
  },

  /**
   * 检查角色名称是否存在
   * @param {string} name - 角色名称
   * @param {number} excludeId - 排除的角色ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkRoleName(name, excludeId = null) {
    const params = { name }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/exists/name`, { params })
    return response.data
  },

  /**
   * 更新角色状态
   * @param {number} id - 角色ID
   * @param {string} status - 状态
   * @returns {Promise}
   */
  async updateRoleStatus(id, status) {
    const response = await api.patch(`${API_URL}/${id}/status`, { status })
    return response.data
  },

  /**
   * 获取角色模板列表
   * @returns {Promise}
   */
  async getRoleTemplates() {
    // 返回预定义的角色模板
    return {
      success: true,
      data: [
        {
          code: 'ADMIN_TEMPLATE',
          name: '管理员模板',
          description: '具有系统管理权限的角色模板',
          category: '系统管理',
          permissionCodes: ['USER_MANAGEMENT', 'ROLE_MANAGEMENT', 'PERMISSION_MANAGEMENT'],
          sortOrder: 1,
          isBuiltIn: true
        },
        {
          code: 'EDITOR_TEMPLATE',
          name: '编辑员模板',
          description: '具有内容编辑权限的角色模板',
          category: '内容管理',
          permissionCodes: ['CONTENT_CREATE', 'CONTENT_UPDATE', 'CONTENT_DELETE'],
          sortOrder: 2,
          isBuiltIn: true
        },
        {
          code: 'VIEWER_TEMPLATE',
          name: '查看员模板',
          description: '只具有查看权限的角色模板',
          category: '基础权限',
          permissionCodes: ['CONTENT_VIEW'],
          sortOrder: 3,
          isBuiltIn: true
        }
      ]
    }
  }
}

export default roleService
