<template>
  <el-header style="border-bottom: 1px solid #e5e7eb; height: 64px; padding: 0 20px; display: flex; align-items: center; justify-content: space-between; background-color: #fff;">
    <!-- 左侧面包屑 -->
    <el-breadcrumb separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">
        <el-icon><HomeFilled /></el-icon> 首页
      </el-breadcrumb-item>
      <el-breadcrumb-item v-if="currentRouteName !== 'Dashboard' && currentRouteName !== '首页'">
        {{ currentRouteName }}
      </el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 右侧工具栏 -->
    <div class="header-toolbar">
      <el-input placeholder="搜索..." :prefix-icon="Search" size="small" style="width: 200px;" class="hidden md:block"/>
      <el-badge :value="3" class="item" type="danger">
        <el-button link>
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>
      <el-tooltip content="全屏" placement="bottom">
        <el-button link @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </el-header>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';

// Element Plus Icons
import {
  ArrowRight, HomeFilled, Search, Bell, FullScreen
} from '@element-plus/icons-vue';

const route = useRoute();

// Compute current route name for breadcrumb
const currentRouteName = computed(() => {
  return route.meta.title || route.name || '页面';
});

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
    });
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};
</script>

<style scoped>
/* Header specific styles */
.el-header {
  background-color: #ffffff;
  color: #333;
  line-height: 64px;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px !important;
}

/* Header Toolbar adjustments */
.header-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-toolbar .el-input--small .el-input__wrapper {
  border-radius: 6px;
  background-color: #f8f9fa;
}

.header-toolbar .el-badge .el-button {
  font-size: 20px;
  color: #6b7280;
}

.header-toolbar .el-badge .el-badge__content {
  border: none;
  transform: translate(30%, -30%);
}

.header-toolbar > .el-button {
  font-size: 20px;
  color: #6b7280;
}
</style>