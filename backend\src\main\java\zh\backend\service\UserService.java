package zh.backend.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import zh.backend.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     */
    User createUser(User user);

    /**
     * 更新用户
     */
    User updateUser(Long id, User user);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 根据ID查找用户
     */
    User findById(Long id);

    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);

    /**
     * 根据用户名查找用户（包含角色和权限）
     */
    User findByUsernameWithRolesAndPermissions(String username);

    /**
     * 根据邮箱查找用户
     */
    User findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    User findByUsernameOrEmail(String usernameOrEmail);

    /**
     * 查找所有用户
     */
    List<User> findAll();

    /**
     * 分页查询用户
     */
    Page<User> findByKeyword(String keyword, Pageable pageable);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 更新用户状态
     */
    void updateUserStatus(Long id, User.UserStatus status);

    /**
     * 重置用户密码
     */
    void resetPassword(Long id, String newPassword);

    /**
     * 修改用户密码
     */
    void changePassword(Long id, String oldPassword, String newPassword);

    /**
     * 锁定用户
     */
    void lockUser(Long id);

    /**
     * 解锁用户
     */
    void unlockUser(Long id);

    /**
     * 激活用户
     */
    void activateUser(Long id);

    /**
     * 停用用户
     */
    void deactivateUser(Long id);

    /**
     * 批量删除用户
     */
    void deleteUsers(List<Long> ids);

    /**
     * 获取用户统计信息
     */
    UserStatistics getUserStatistics();

    /**
     * 根据角色查找用户
     */
    List<User> findByRoleCode(String roleCode);

    /**
     * 根据权限查找用户
     */
    List<User> findByPermissionCode(String permissionCode);

    /**
     * 更新用户最后登录时间
     */
    void updateLastLoginTime(Long id);

    /**
     * 用户统计信息
     */
    class UserStatistics {
        private long totalUsers;
        private long activeUsers;
        private long lockedUsers;
        private long inactiveUsers;

        public UserStatistics() {}

        public UserStatistics(long totalUsers, long activeUsers, long lockedUsers, long inactiveUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.lockedUsers = lockedUsers;
            this.inactiveUsers = inactiveUsers;
        }

        public long getTotalUsers() { return totalUsers; }
        public void setTotalUsers(long totalUsers) { this.totalUsers = totalUsers; }

        public long getActiveUsers() { return activeUsers; }
        public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }

        public long getLockedUsers() { return lockedUsers; }
        public void setLockedUsers(long lockedUsers) { this.lockedUsers = lockedUsers; }

        public long getInactiveUsers() { return inactiveUsers; }
        public void setInactiveUsers(long inactiveUsers) { this.inactiveUsers = inactiveUsers; }
    }
}
