package zh.backend.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("message", "动态权限管理系统运行正常");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("application", "动态权限管理系统");
        response.put("version", "1.0.0");
        response.put("description", "基于Spring Boot 3.3.5 + Java 17的动态权限管理系统");
        response.put("features", new String[]{
            "RBAC权限模型",
            "动态权限分配",
            "细粒度权限控制",
            "权限缓存机制",
            "操作审计日志"
        });
        return ResponseEntity.ok(response);
    }
}
