import { ElMessage } from 'element-plus'

/**
 * 统一的API错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 */
export const handleApiError = (error, defaultMessage = '操作失败') => {
  console.error('API Error:', error)
  
  let message = defaultMessage
  
  if (error.response) {
    // 服务器响应了错误状态码
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        message = data?.message || '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        // 可以在这里处理登录跳转
        break
      case 403:
        message = '没有权限执行此操作'
        break
      case 404:
        message = '请求的资源不存在'
        break
      case 409:
        message = data?.message || '数据冲突'
        break
      case 422:
        message = data?.message || '数据验证失败'
        break
      case 500:
        message = '服务器内部错误'
        break
      default:
        message = data?.message || `请求失败 (${status})`
    }
  } else if (error.request) {
    // 请求已发出但没有收到响应
    message = '网络连接失败，请检查网络'
  } else {
    // 其他错误
    message = error.message || defaultMessage
  }
  
  ElMessage.error(message)
  return message
}

/**
 * 处理表单验证错误
 * @param {Object} errors - 验证错误对象
 */
export const handleValidationErrors = (errors) => {
  if (errors && typeof errors === 'object') {
    const firstError = Object.values(errors)[0]
    if (Array.isArray(firstError) && firstError.length > 0) {
      ElMessage.error(firstError[0])
    } else if (typeof firstError === 'string') {
      ElMessage.error(firstError)
    }
  }
}

/**
 * 处理业务逻辑错误
 * @param {Object} response - API响应对象
 */
export const handleBusinessError = (response) => {
  if (response && !response.success) {
    ElMessage.error(response.message || '操作失败')
    return false
  }
  return true
}

export default {
  handleApiError,
  handleValidationErrors,
  handleBusinessError
}
