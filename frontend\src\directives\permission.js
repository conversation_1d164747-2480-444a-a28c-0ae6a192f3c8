import { useAuthStore } from '@/stores/auth';
import { hasPermission } from '@/utils/permissions';

export const canDirective = {
  mounted(el, binding) {
    const authStore = useAuthStore();
    const requiredPermission = binding.value; // 'user:create'
    const arg = binding.arg; // 'any' or 'all', if provided like v-can:any
    const mode = arg === 'all' ? 'all' : 'any';

    if (!authStore.isAuthenticated) {
      el.style.display = 'none';
      return;
    }

    const userHasPermission = hasPermission(authStore.user, requiredPermission, mode);

    if (!userHasPermission) {
      el.style.display = 'none';
    }
  },
  // 可选：在组件更新时也检查权限，以防用户权限动态变化且store已更新
  updated(el, binding) {
    const authStore = useAuthStore();
    const requiredPermission = binding.value;
    const arg = binding.arg;
    const mode = arg === 'all' ? 'all' : 'any';

    if (!authStore.isAuthenticated) {
      el.style.display = 'none';
      return;
    }
    
    const userHasPermission = hasPermission(authStore.user, requiredPermission, mode);

    if (!userHasPermission) {
      el.style.display = 'none';
    } else {
      // 如果权限检查通过，确保元素可见（以防之前被隐藏）
      // 注意：这可能会覆盖其他控制display的逻辑，需要谨慎使用或根据项目具体情况调整
      el.style.display = ''; 
    }
  }
};