<template>
  <div class="home-container">
    <h1>欢迎使用我们的系统</h1>
    
    <div v-if="user" class="user-info-card">
      <h2>用户信息</h2>
      <div class="info-item">
        <span class="label">用户名:</span>
        <span class="value">{{ user.username }}</span>
      </div>
      <div v-if="user.email" class="info-item">
        <span class="label">邮箱:</span>
        <span class="value">{{ user.email }}</span>
      </div>
      <div v-if="user.realName" class="info-item">
        <span class="label">真实姓名:</span>
        <span class="value">{{ user.realName }}</span>
      </div>
      <div class="info-item">
        <span class="label">角色:</span>
        <span class="value">{{ formatRoles(user.roles) }}</span>
      </div>
      <div v-if="user.permissions && user.permissions.length > 0" class="info-item">
        <span class="label">权限:</span>
        <span class="value">{{ formatPermissions(user.permissions) }}</span>
      </div>
    </div>

    <div class="dashboard-cards">
      <div class="card">
        <h3>快速导航</h3>
        <ul>
          <li><router-link to="/about">关于我们</router-link></li>
          <li><a href="#" @click.prevent="refreshUserInfo">刷新用户信息</a></li>
          <li><a href="#" @click.prevent="refreshToken">刷新令牌</a></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import axios from 'axios';

const authStore = useAuthStore();
const user = computed(() => authStore.user);
const loading = ref(false);
const message = ref('');

onMounted(() => {
  refreshUserInfo();
});

const refreshUserInfo = async () => {
  loading.value = true;
  message.value = '正在刷新用户信息...';
  try {
    await authStore.fetchUser();
    message.value = '用户信息已更新';
    setTimeout(() => {
      message.value = '';
    }, 3000);
  } catch (error) {
    message.value = `更新失败: ${error.message}`;
  } finally {
    loading.value = false;
  }
};

const refreshToken = async () => {
  loading.value = true;
  message.value = '正在刷新令牌...';
  try {
    const response = await axios.post('/api/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${authStore.token}` }
    });
    
    if (response.data && response.data.token) {
      // 更新 Pinia store 中的 token
      authStore.token = response.data.token;
      localStorage.setItem('token', response.data.token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
      
      message.value = '令牌已刷新';
      setTimeout(() => {
        message.value = '';
      }, 3000);
    }
  } catch (error) {
    message.value = `刷新令牌失败: ${error.response?.data?.message || error.message}`;
  } finally {
    loading.value = false;
  }
};

const formatRoles = (roles) => {
  if (!roles || roles.length === 0) return '无';
  return roles.join(', ');
};

const formatPermissions = (permissions) => {
  if (!permissions || permissions.length === 0) return '无';
  return permissions.join(', ');
};
</script>

<style scoped>
.home-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.user-info-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-info-card h2 {
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  color: #444;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
}

.label {
  font-weight: bold;
  min-width: 100px;
  color: #555;
}

.value {
  color: #333;
  flex: 1;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.card ul {
  list-style-type: none;
  padding: 0;
}

.card li {
  margin-bottom: 10px;
}

.card a {
  color: #007bff;
  text-decoration: none;
}

.card a:hover {
  text-decoration: underline;
}

.message {
  margin: 20px 0;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
}
</style>
