package zh.backend.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import zh.backend.entity.Role;
import zh.backend.entity.Permission;

import java.util.List;
import java.util.Set;

/**
 * 角色服务接口
 */
public interface RoleService {

    /**
     * 创建角色
     */
    Role createRole(Role role);

    /**
     * 更新角色
     */
    Role updateRole(Long id, Role role);

    /**
     * 删除角色
     */
    void deleteRole(Long id);

    /**
     * 根据ID查找角色
     */
    Role findById(Long id);

    /**
     * 根据编码查找角色
     */
    Role findByCode(String code);

    /**
     * 查找所有角色
     */
    List<Role> findAll();

    /**
     * 分页查询角色（支持状态和类型过滤）
     */
    Page<Role> findByKeyword(String keyword, Role.RoleStatus status, Boolean isSystem, Pageable pageable);

    /**
     * 查找激活的角色
     */
    List<Role> findActiveRoles();

    /**
     * 为角色分配权限
     */
    void assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 从角色移除权限
     */
    void removePermissionsFromRole(Long roleId, List<Long> permissionIds);

    /**
     * 获取角色的权限
     */
    List<Permission> getRolePermissions(Long roleId);

    /**
     * 为用户分配角色
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 从用户移除角色
     */
    void removeRolesFromUser(Long userId, List<Long> roleIds);

    /**
     * 获取用户的角色
     */
    List<Role> getUserRoles(Long userId);

    /**
     * 获取用户的角色编码
     */
    Set<String> getUserRoleCodes(Long userId);

    /**
     * 检查用户是否拥有指定角色
     */
    boolean hasRole(Long userId, String roleCode);

    /**
     * 检查用户是否拥有任意一个角色
     */
    boolean hasAnyRole(Long userId, String... roleCodes);

    /**
     * 检查用户是否拥有所有角色
     */
    boolean hasAllRoles(Long userId, String... roleCodes);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 批量删除角色
     */
    void deleteRoles(List<Long> ids);

    /**
     * 复制角色权限
     */
    void copyRolePermissions(Long sourceRoleId, Long targetRoleId);

    /**
     * 获取角色统计信息
     */
    RoleStatistics getRoleStatistics();

    /**
     * 角色统计信息
     */
    class RoleStatistics {
        private long totalRoles;
        private long activeRoles;
        private long systemRoles;
        private long customRoles;

        // 构造函数、getter和setter
        public RoleStatistics() {}

        public RoleStatistics(long totalRoles, long activeRoles, long systemRoles, long customRoles) {
            this.totalRoles = totalRoles;
            this.activeRoles = activeRoles;
            this.systemRoles = systemRoles;
            this.customRoles = customRoles;
        }

        public long getTotalRoles() { return totalRoles; }
        public void setTotalRoles(long totalRoles) { this.totalRoles = totalRoles; }

        public long getActiveRoles() { return activeRoles; }
        public void setActiveRoles(long activeRoles) { this.activeRoles = activeRoles; }

        public long getSystemRoles() { return systemRoles; }
        public void setSystemRoles(long systemRoles) { this.systemRoles = systemRoles; }

        public long getCustomRoles() { return customRoles; }
        public void setCustomRoles(long customRoles) { this.customRoles = customRoles; }
    }
}
