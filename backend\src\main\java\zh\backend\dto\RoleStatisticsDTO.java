package zh.backend.dto;

/**
 * 角色统计信息DTO
 */
public class RoleStatisticsDTO {
    private long total;
    private long active;
    private long inactive;
    private long system;
    private long custom;
    private long totalUsers;
    private long totalPermissions;
    private double averagePermissionsPerRole;
    private double averageUsersPerRole;

    // 构造函数
    public RoleStatisticsDTO() {}

    public RoleStatisticsDTO(long total, long active, long inactive, long system, long custom, 
                           long totalUsers, long totalPermissions) {
        this.total = total;
        this.active = active;
        this.inactive = inactive;
        this.system = system;
        this.custom = custom;
        this.totalUsers = totalUsers;
        this.totalPermissions = totalPermissions;
        
        // 计算平均值
        this.averagePermissionsPerRole = total > 0 ? (double) totalPermissions / total : 0.0;
        this.averageUsersPerRole = total > 0 ? (double) totalUsers / total : 0.0;
    }

    // Getters and Setters
    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getActive() {
        return active;
    }

    public void setActive(long active) {
        this.active = active;
    }

    public long getInactive() {
        return inactive;
    }

    public void setInactive(long inactive) {
        this.inactive = inactive;
    }

    public long getSystem() {
        return system;
    }

    public void setSystem(long system) {
        this.system = system;
    }

    public long getCustom() {
        return custom;
    }

    public void setCustom(long custom) {
        this.custom = custom;
    }

    public long getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(long totalUsers) {
        this.totalUsers = totalUsers;
    }

    public long getTotalPermissions() {
        return totalPermissions;
    }

    public void setTotalPermissions(long totalPermissions) {
        this.totalPermissions = totalPermissions;
    }

    public double getAveragePermissionsPerRole() {
        return averagePermissionsPerRole;
    }

    public void setAveragePermissionsPerRole(double averagePermissionsPerRole) {
        this.averagePermissionsPerRole = averagePermissionsPerRole;
    }

    public double getAverageUsersPerRole() {
        return averageUsersPerRole;
    }

    public void setAverageUsersPerRole(double averageUsersPerRole) {
        this.averageUsersPerRole = averageUsersPerRole;
    }
}
