<template>
  <div class="user-permission-manager">
    <el-card class="manager-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <el-icon><User /></el-icon>
            <span>用户权限管理</span>
            <el-tag v-if="currentUser" type="primary">{{ currentUser.username }}</el-tag>
          </div>
          <div class="header-actions">
            <el-button @click="handleRefresh" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 用户选择 -->
      <div class="user-selector">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-select
              v-model="selectedUserId"
              placeholder="选择用户"
              filterable
              remote
              :remote-method="searchUsers"
              :loading="userLoading"
              @change="handleUserChange"
              style="width: 100%"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.id"
                :label="`${user.username} (${user.realName})`"
                :value="user.id"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-button-group>
              <el-button
                :type="viewMode === 'direct' ? 'primary' : ''"
                @click="viewMode = 'direct'"
              >
                直接权限
              </el-button>
              <el-button
                :type="viewMode === 'role' ? 'primary' : ''"
                @click="viewMode = 'role'"
              >
                角色权限
              </el-button>
              <el-button
                :type="viewMode === 'all' ? 'primary' : ''"
                @click="viewMode = 'all'"
              >
                全部权限
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </div>

      <!-- 权限操作区 -->
      <div v-if="selectedUserId" class="permission-actions">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-button
              type="primary"
              @click="handleGrantPermission"
              :disabled="!hasGrantPermission"
            >
              <el-icon><Plus /></el-icon>
              分配权限
            </el-button>
            <el-button
              type="danger"
              @click="handleBatchRevoke"
              :disabled="selectedPermissions.length === 0 || !hasRevokePermission"
            >
              <el-icon><Minus /></el-icon>
              批量撤销 ({{ selectedPermissions.length }})
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-input
              v-model="permissionSearchKeyword"
              placeholder="搜索权限"
              clearable
              @input="handlePermissionSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 权限列表 -->
      <div v-if="selectedUserId" class="permission-list" v-loading="permissionLoading">
        <el-table
          :data="filteredPermissions"
          @selection-change="handleSelectionChange"
          row-key="id"
          class="permission-table"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="isSelectable"
          />
          
          <el-table-column prop="name" label="权限名称" min-width="150">
            <template #default="{ row }">
              <div class="permission-name">
                <el-icon class="permission-icon">
                  <Menu v-if="row.resourceType === 'MENU'" />
                  <Button v-else-if="row.resourceType === 'BUTTON'" />
                  <Link v-else-if="row.resourceType === 'API'" />
                  <Document v-else />
                </el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="code" label="权限编码" min-width="120">
            <template #default="{ row }">
              <code class="permission-code">{{ row.code }}</code>
            </template>
          </el-table-column>
          
          <el-table-column prop="resourceType" label="资源类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getResourceTypeTag(row.resourceType)" size="small">
                {{ formatResourceType(row.resourceType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="source" label="来源" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.source === 'DIRECT' ? 'success' : 'info'"
                size="small"
              >
                {{ row.source === 'DIRECT' ? '直接分配' : '角色继承' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="grantedAt" label="分配时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.grantedAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="expiresAt" label="过期时间" width="150">
            <template #default="{ row }">
              <span v-if="row.expiresAt">
                {{ formatDate(row.expiresAt) }}
                <el-tag
                  v-if="isExpired(row.expiresAt)"
                  type="danger"
                  size="small"
                >
                  已过期
                </el-tag>
              </span>
              <span v-else class="no-expiry">永久</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.source === 'DIRECT'"
                type="danger"
                size="small"
                @click="handleRevokePermission(row)"
                :disabled="!hasRevokePermission"
              >
                <el-icon><Minus /></el-icon>
                撤销
              </el-button>
              <el-button
                v-else
                type="info"
                size="small"
                @click="handleViewRole(row)"
              >
                <el-icon><View /></el-icon>
                查看角色
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="请选择用户查看权限信息">
          <el-button type="primary" @click="handleSelectUser">
            <el-icon><User /></el-icon>
            选择用户
          </el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 分配权限对话框 -->
    <PermissionGrantDialog
      v-model:visible="grantDialogVisible"
      :user-id="selectedUserId"
      :existing-permissions="userPermissions"
      @success="handleGrantSuccess"
    />

    <!-- 角色查看对话框 -->
    <RoleViewDialog
      v-model:visible="roleDialogVisible"
      :role="currentRole"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  User, Refresh, Plus, Minus, Search, Menu, Button, Link, Document, View
} from '@element-plus/icons-vue'
import { permissionService } from '@/api/permissionService'
import { userService } from '@/api/userService'
import { useUserStore } from '@/stores/user'
import PermissionGrantDialog from './PermissionGrantDialog.vue'
import RoleViewDialog from './RoleViewDialog.vue'

// Store
const userStore = useUserStore()

// 响应式数据
const selectedUserId = ref(null)
const currentUser = ref(null)
const viewMode = ref('all') // direct | role | all
const userOptions = ref([])
const userLoading = ref(false)
const permissionLoading = ref(false)
const userPermissions = ref([])
const selectedPermissions = ref([])
const permissionSearchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const grantDialogVisible = ref(false)
const roleDialogVisible = ref(false)
const currentRole = ref(null)

// 计算属性
const hasGrantPermission = computed(() => {
  return userStore.hasPermission('PERMISSION_UPDATE')
})

const hasRevokePermission = computed(() => {
  return userStore.hasPermission('PERMISSION_UPDATE')
})

const filteredPermissions = computed(() => {
  let permissions = userPermissions.value

  // 根据视图模式过滤
  if (viewMode.value === 'direct') {
    permissions = permissions.filter(p => p.source === 'DIRECT')
  } else if (viewMode.value === 'role') {
    permissions = permissions.filter(p => p.source === 'ROLE')
  }

  // 根据搜索关键词过滤
  if (permissionSearchKeyword.value) {
    const keyword = permissionSearchKeyword.value.toLowerCase()
    permissions = permissions.filter(p =>
      p.name.toLowerCase().includes(keyword) ||
      p.code.toLowerCase().includes(keyword)
    )
  }

  return permissions
})

// 方法
const searchUsers = async (query) => {
  if (!query) {
    userOptions.value = []
    return
  }

  try {
    userLoading.value = true
    const response = await userService.searchUsers({ keyword: query, size: 20 })
    userOptions.value = response.data?.content || []
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    userLoading.value = false
  }
}

const handleUserChange = async (userId) => {
  if (!userId) {
    currentUser.value = null
    userPermissions.value = []
    return
  }

  try {
    // 获取用户信息
    const userResponse = await userService.getUserById(userId)
    currentUser.value = userResponse.data

    // 加载用户权限
    await loadUserPermissions()
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

const loadUserPermissions = async () => {
  if (!selectedUserId.value) return

  try {
    permissionLoading.value = true
    const response = await permissionService.getAllUserPermissions(selectedUserId.value)
    userPermissions.value = response.data || []
    total.value = userPermissions.value.length
  } catch (error) {
    console.error('获取用户权限失败:', error)
    ElMessage.error('获取用户权限失败')
    userPermissions.value = []
  } finally {
    permissionLoading.value = false
  }
}

const handleRefresh = () => {
  if (selectedUserId.value) {
    loadUserPermissions()
  }
}

const handleSelectionChange = (selection) => {
  selectedPermissions.value = selection
}

const isSelectable = (row) => {
  return row.source === 'DIRECT' && hasRevokePermission.value
}

const handleGrantPermission = () => {
  grantDialogVisible.value = true
}

const handleBatchRevoke = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销选中的 ${selectedPermissions.value.length} 个权限吗？`,
      '批量撤销权限',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    for (const permission of selectedPermissions.value) {
      await permissionService.revokePermissionFromUser(selectedUserId.value, permission.id)
    }

    ElNotification.success({
      title: '撤销成功',
      message: `已撤销 ${selectedPermissions.value.length} 个权限`
    })

    selectedPermissions.value = []
    loadUserPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量撤销权限失败')
    }
  }
}

const handleRevokePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销权限"${permission.name}"吗？`,
      '撤销权限',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await permissionService.revokePermissionFromUser(selectedUserId.value, permission.id)

    ElNotification.success({
      title: '撤销成功',
      message: `权限"${permission.name}"已撤销`
    })

    loadUserPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '撤销权限失败')
    }
  }
}

const handleViewRole = (permission) => {
  // 这里应该获取角色信息并显示
  currentRole.value = permission.role
  roleDialogVisible.value = true
}

const handleGrantSuccess = () => {
  loadUserPermissions()
}

const handlePermissionSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

const handleSelectUser = () => {
  // 可以打开用户选择对话框或其他操作
}

const getResourceTypeTag = (type) => {
  const tags = {
    MENU: 'success',
    BUTTON: 'warning',
    API: 'info',
    OTHER: ''
  }
  return tags[type] || ''
}

const formatResourceType = (type) => {
  const types = {
    MENU: '菜单',
    BUTTON: '按钮',
    API: '接口',
    OTHER: '其他'
  }
  return types[type] || type
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const isExpired = (dateString) => {
  if (!dateString) return false
  return new Date(dateString) < new Date()
}

// 监听器
watch(viewMode, () => {
  currentPage.value = 1
})

// 生命周期
onMounted(() => {
  // 初始化时可以加载一些默认用户
})
</script>

<style scoped>
.user-permission-manager {
  padding: 20px;
}

.manager-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.user-selector {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.permission-actions {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.permission-list {
  margin-bottom: 20px;
}

.permission-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-icon {
  color: #409eff;
}

.permission-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.no-expiry {
  color: #67c23a;
  font-weight: 500;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-permission-manager {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .user-selector .el-col,
  .permission-actions .el-col {
    margin-bottom: 12px;
  }
  
  .pagination-container {
    justify-content: center;
  }
}
</style>
