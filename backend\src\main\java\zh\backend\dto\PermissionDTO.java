package zh.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import zh.backend.entity.Permission;

import java.time.LocalDateTime;

/**
 * 权限数据传输对象
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PermissionDTO {
    
    private Long id;
    
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String name;
    
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    private String code;
    
    private String description;
    
    @NotNull(message = "资源类型不能为空")
    private Permission.ResourceType resourceType;
    
    private String resourcePath;
    
    private Permission.HttpMethod httpMethod;
    
    private Long parentId;
    
    private String parentName;
    
    private Integer level;
    
    private Integer sortOrder;
    
    private Permission.PermissionStatus status;
    
    private Boolean isSystem;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private Long createdBy;
    
    private Long updatedBy;
    
    // 构造函数
    public PermissionDTO() {}
    
    public PermissionDTO(Permission permission) {
        this.id = permission.getId();
        this.name = permission.getName();
        this.code = permission.getCode();
        this.description = permission.getDescription();
        this.resourceType = permission.getResourceType();
        this.resourcePath = permission.getResourcePath();
        this.httpMethod = permission.getHttpMethod();
        this.parentId = permission.getParentId();
        this.level = permission.getLevel();
        this.sortOrder = permission.getSortOrder();
        this.status = permission.getStatus();
        this.isSystem = permission.getIsSystem();
        this.createdAt = permission.getCreatedAt();
        this.updatedAt = permission.getUpdatedAt();
        this.createdBy = permission.getCreatedBy();
        this.updatedBy = permission.getUpdatedBy();
        
        // 设置父权限名称
        if (permission.getParent() != null) {
            this.parentName = permission.getParent().getName();
        }
    }
    
    // 转换为实体
    public Permission toEntity() {
        Permission permission = new Permission();
        permission.setId(this.id);
        permission.setName(this.name);
        permission.setCode(this.code);
        permission.setDescription(this.description);
        permission.setResourceType(this.resourceType);
        permission.setResourcePath(this.resourcePath);
        permission.setHttpMethod(this.httpMethod);
        permission.setParentId(this.parentId);
        permission.setLevel(this.level);
        permission.setSortOrder(this.sortOrder);
        permission.setStatus(this.status);
        permission.setIsSystem(this.isSystem);
        return permission;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Permission.ResourceType getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(Permission.ResourceType resourceType) {
        this.resourceType = resourceType;
    }
    
    public String getResourcePath() {
        return resourcePath;
    }
    
    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }
    
    public Permission.HttpMethod getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(Permission.HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getParentName() {
        return parentName;
    }
    
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Permission.PermissionStatus getStatus() {
        return status;
    }
    
    public void setStatus(Permission.PermissionStatus status) {
        this.status = status;
    }
    
    public Boolean getIsSystem() {
        return isSystem;
    }
    
    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Long getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }
    
    public Long getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }
}
