# 动态权限管理系统 - 权限表数据说明

## 📋 权限表结构

### 表名：permissions

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | BIGINT | 主键ID | 1 |
| name | VARCHAR(100) | 权限名称 | "用户管理" |
| code | VARCHAR(100) | 权限编码 | "USER_MANAGEMENT" |
| description | TEXT | 权限描述 | "用户管理模块" |
| resource_type | ENUM | 资源类型 | MENU/BUTTON/API/DATA |
| resource_path | VARCHAR(255) | 资源路径 | "/admin/users" |
| http_method | ENUM | HTTP方法 | GET/POST/PUT/DELETE/PATCH/ALL |
| parent_id | BIGINT | 父权限ID | NULL或父权限ID |
| level | INT | 权限层级 | 1(模块级)/2(功能级)/3(按钮级) |
| sort_order | INT | 排序 | 1,2,3... |
| status | ENUM | 状态 | ACTIVE/INACTIVE |
| is_system | BOOLEAN | 是否系统权限 | TRUE/FALSE |

## 🏗️ 权限层级结构

```
Level 1 (模块级) - 菜单权限
├── Level 2 (功能级) - API权限
│   ├── 查看列表
│   ├── 创建
│   ├── 编辑
│   ├── 删除
│   └── 其他操作
└── Level 3 (按钮级) - UI控制权限
    ├── 创建按钮
    ├── 编辑按钮
    └── 删除按钮
```

## 📊 完整权限数据

### 1. 用户管理模块 (ID: 1-8)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 1 | USER_MANAGEMENT | 用户管理 | MENU | /admin/users | ALL | NULL | 1 |
| 2 | USER_LIST | 查看用户列表 | API | /api/users | GET | 1 | 2 |
| 3 | USER_CREATE | 创建用户 | API | /api/users | POST | 1 | 2 |
| 4 | USER_UPDATE | 编辑用户 | API | /api/users/* | PUT | 1 | 2 |
| 5 | USER_DELETE | 删除用户 | API | /api/users/* | DELETE | 1 | 2 |
| 6 | USER_RESET_PASSWORD | 重置用户密码 | API | /api/users/*/reset-password | POST | 1 | 2 |
| 7 | USER_STATUS_MANAGE | 用户状态管理 | API | /api/users/*/status | PATCH | 1 | 2 |
| 8 | USER_ROLE_ASSIGN | 用户角色分配 | API | /api/users/*/roles | POST | 1 | 2 |

### 2. 角色管理模块 (ID: 10-16)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 10 | ROLE_MANAGEMENT | 角色管理 | MENU | /admin/roles | ALL | NULL | 1 |
| 11 | ROLE_LIST | 查看角色列表 | API | /api/roles | GET | 10 | 2 |
| 12 | ROLE_CREATE | 创建角色 | API | /api/roles | POST | 10 | 2 |
| 13 | ROLE_UPDATE | 编辑角色 | API | /api/roles/* | PUT | 10 | 2 |
| 14 | ROLE_DELETE | 删除角色 | API | /api/roles/* | DELETE | 10 | 2 |
| 15 | ROLE_ASSIGN_PERMISSIONS | 分配角色权限 | API | /api/roles/*/permissions | POST | 10 | 2 |
| 16 | ROLE_USER_MANAGE | 角色用户管理 | API | /api/roles/*/users | POST | 10 | 2 |

### 3. 权限管理模块 (ID: 20-25)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 20 | PERMISSION_MANAGEMENT | 权限管理 | MENU | /admin/permissions | ALL | NULL | 1 |
| 21 | PERMISSION_LIST | 查看权限列表 | API | /api/permissions | GET | 20 | 2 |
| 22 | PERMISSION_CREATE | 创建权限 | API | /api/permissions | POST | 20 | 2 |
| 23 | PERMISSION_UPDATE | 编辑权限 | API | /api/permissions/* | PUT | 20 | 2 |
| 24 | PERMISSION_DELETE | 删除权限 | API | /api/permissions/* | DELETE | 20 | 2 |
| 25 | PERMISSION_TREE | 权限树查看 | API | /api/permissions/tree | GET | 20 | 2 |

### 4. 系统监控模块 (ID: 30-33)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 30 | SYSTEM_MONITOR | 系统监控 | MENU | /admin/monitor | ALL | NULL | 1 |
| 31 | LOG_VIEW | 查看系统日志 | API | /api/logs | GET | 30 | 2 |
| 32 | ONLINE_USERS | 在线用户管理 | API | /api/online-users | GET | 30 | 2 |
| 33 | HEALTH_CHECK | 系统健康检查 | API | /api/health | GET | 30 | 2 |

### 5. 个人中心模块 (ID: 40-43)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 40 | PROFILE | 个人中心 | MENU | /profile | ALL | NULL | 1 |
| 41 | PROFILE_VIEW | 查看个人信息 | API | /api/profile | GET | 40 | 2 |
| 42 | PROFILE_UPDATE | 修改个人信息 | API | /api/profile | PUT | 40 | 2 |
| 43 | PASSWORD_CHANGE | 修改密码 | API | /api/profile/password | PUT | 40 | 2 |

### 6. 仪表盘模块 (ID: 50-52)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 50 | DASHBOARD | 仪表盘 | MENU | /dashboard | ALL | NULL | 1 |
| 51 | DASHBOARD_STATS | 查看统计数据 | API | /api/dashboard/stats | GET | 50 | 2 |
| 52 | DASHBOARD_SYSTEM_STATUS | 查看系统状态 | API | /api/dashboard/system-status | GET | 50 | 2 |

### 7. 认证管理模块 (ID: 60-64)

| ID | 权限编码 | 权限名称 | 资源类型 | 资源路径 | HTTP方法 | 父ID | 层级 |
|----|----------|----------|----------|----------|----------|------|------|
| 60 | AUTH_MANAGEMENT | 认证管理 | API | /api/auth | ALL | NULL | 1 |
| 61 | AUTH_LOGIN | 用户登录 | API | /api/auth/login | POST | 60 | 2 |
| 62 | AUTH_REGISTER | 用户注册 | API | /api/auth/register | POST | 60 | 2 |
| 63 | AUTH_LOGOUT | 用户登出 | API | /api/auth/logout | POST | 60 | 2 |
| 64 | AUTH_ME | 获取用户信息 | API | /api/auth/me | GET | 60 | 2 |

## 🎯 角色权限分配

### 超级管理员 (SUPER_ADMIN)
- **权限范围**: 所有权限
- **说明**: 拥有系统的完全控制权

### 系统管理员 (SYSTEM_ADMIN)
- **用户管理**: USER_MANAGEMENT, USER_LIST, USER_CREATE, USER_UPDATE, USER_RESET_PASSWORD, USER_STATUS_MANAGE, USER_ROLE_ASSIGN
- **角色管理**: ROLE_MANAGEMENT, ROLE_LIST, ROLE_CREATE, ROLE_UPDATE, ROLE_ASSIGN_PERMISSIONS, ROLE_USER_MANAGE
- **权限管理**: PERMISSION_MANAGEMENT, PERMISSION_LIST, PERMISSION_TREE (只读)
- **系统监控**: SYSTEM_MONITOR, LOG_VIEW, ONLINE_USERS, HEALTH_CHECK
- **仪表盘**: DASHBOARD, DASHBOARD_STATS, DASHBOARD_SYSTEM_STATUS
- **个人中心**: PROFILE, PROFILE_VIEW, PROFILE_UPDATE, PASSWORD_CHANGE
- **认证**: AUTH_MANAGEMENT, AUTH_LOGIN, AUTH_LOGOUT, AUTH_ME

### 普通用户 (USER)
- **仪表盘**: DASHBOARD, DASHBOARD_STATS
- **个人中心**: PROFILE, PROFILE_VIEW, PROFILE_UPDATE, PASSWORD_CHANGE
- **认证**: AUTH_MANAGEMENT, AUTH_LOGIN, AUTH_REGISTER, AUTH_LOGOUT, AUTH_ME

## 🔧 使用说明

### 后端权限注解
```java
@RequirePermission("USER_LIST")
@GetMapping("/api/users")
public ResponseEntity<?> getUsers() { }
```

### 前端权限指令
```vue
<el-button v-can="'USER_CREATE'">创建用户</el-button>
```

### 权限检查函数
```javascript
import { hasPermission } from '@/utils/permissions'

if (hasPermission(user, 'USER_CREATE')) {
  // 显示创建按钮
}
```

## 📝 注意事项

1. **权限编码规范**: 使用大写字母和下划线，格式为 `MODULE_ACTION`
2. **层级关系**: 通过 parent_id 建立权限的层级关系
3. **系统权限**: is_system=TRUE 的权限不允许删除
4. **权限继承**: 拥有父权限自动拥有所有子权限
5. **动态更新**: 权限变更后需要清除相关缓存
