import api from './axios'

const API_URL = '/api/users'

/**
 * 用户管理API服务
 */
export const userService = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getUsers(params = {}) {
    const response = await api.get(API_URL, { params })
    return response.data
  },

  /**
   * 根据ID获取用户详情
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  async getUserById(id) {
    const response = await api.get(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise}
   */
  async createUser(userData) {
    const response = await api.post(API_URL, userData)
    return response.data
  },

  /**
   * 更新用户
   * @param {number} id - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise}
   */
  async updateUser(id, userData) {
    const response = await api.put(`${API_URL}/${id}`, userData)
    return response.data
  },

  /**
   * 删除用户
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  async deleteUser(id) {
    const response = await api.delete(`${API_URL}/${id}`)
    return response.data
  },

  /**
   * 批量删除用户
   * @param {Array} ids - 用户ID数组
   * @returns {Promise}
   */
  async deleteUsers(ids) {
    const response = await api.delete(`${API_URL}/batch`, { data: { ids } })
    return response.data
  },

  /**
   * 获取用户角色
   * @param {number} userId - 用户ID
   * @returns {Promise}
   */
  async getUserRoles(userId) {
    const response = await api.get(`${API_URL}/${userId}/roles`)
    return response.data
  },

  /**
   * 为用户分配角色
   * @param {number} userId - 用户ID
   * @param {Array} roleIds - 角色ID数组
   * @returns {Promise}
   */
  async assignRolesToUser(userId, roleIds) {
    const response = await api.post(`${API_URL}/${userId}/roles`, { roleIds })
    return response.data
  },

  /**
   * 为用户分配角色（别名方法，兼容新的调用方式）
   * @param {number} userId - 用户ID
   * @param {Array} roleIds - 角色ID数组
   * @returns {Promise}
   */
  async assignRoles(userId, roleIds) {
    return this.assignRolesToUser(userId, roleIds)
  },

  /**
   * 从用户中移除角色
   * @param {number} userId - 用户ID
   * @param {Array} roleIds - 角色ID数组
   * @returns {Promise}
   */
  async removeRolesFromUser(userId, roleIds) {
    const response = await api.delete(`${API_URL}/${userId}/roles`, { data: { roleIds } })
    return response.data
  },

  /**
   * 获取用户权限
   * @param {number} userId - 用户ID
   * @returns {Promise}
   */
  async getUserPermissions(userId) {
    const response = await api.get(`${API_URL}/${userId}/permissions`)
    return response.data
  },

  /**
   * 重置用户密码
   * @param {number} id - 用户ID
   * @param {string} newPassword - 新密码
   * @returns {Promise}
   */
  async resetUserPassword(id, newPassword) {
    const response = await api.post(`${API_URL}/${id}/reset-password`, { newPassword })
    return response.data
  },

  /**
   * 更新用户状态
   * @param {number} id - 用户ID
   * @param {string} status - 状态
   * @returns {Promise}
   */
  async updateUserStatus(id, status) {
    const response = await api.patch(`${API_URL}/${id}/status`, { status })
    return response.data
  },

  /**
   * 锁定用户
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  async lockUser(id) {
    const response = await api.post(`${API_URL}/${id}/lock`)
    return response.data
  },

  /**
   * 解锁用户
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  async unlockUser(id) {
    const response = await api.post(`${API_URL}/${id}/unlock`)
    return response.data
  },

  /**
   * 获取用户统计信息
   * @returns {Promise}
   */
  async getUserStats() {
    const response = await api.get(`${API_URL}/statistics`)
    return response.data
  },

  /**
   * 检查用户名是否存在
   * @param {string} username - 用户名
   * @param {number} excludeId - 排除的用户ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkUsername(username, excludeId = null) {
    const params = { username }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/check-username`, { params })
    return response.data
  },

  /**
   * 检查邮箱是否存在
   * @param {string} email - 邮箱
   * @param {number} excludeId - 排除的用户ID（用于编辑时检查）
   * @returns {Promise}
   */
  async checkEmail(email, excludeId = null) {
    const params = { email }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await api.get(`${API_URL}/check-email`, { params })
    return response.data
  },

  /**
   * 修改用户密码
   * @param {number} id - 用户ID
   * @param {string} oldPassword - 旧密码
   * @param {string} newPassword - 新密码
   * @returns {Promise}
   */
  async changePassword(id, oldPassword, newPassword) {
    const response = await api.post(`${API_URL}/${id}/change-password`, { oldPassword, newPassword })
    return response.data
  },

  /**
   * 批量删除用户
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise}
   */
  async deleteUsers(userIds) {
    const response = await api.delete(`${API_URL}/batch`, {
      data: { userIds }
    })
    return response.data
  }
}

export default userService
