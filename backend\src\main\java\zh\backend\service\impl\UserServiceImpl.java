package zh.backend.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zh.backend.entity.User;
import zh.backend.mapper.UserMapper;
import zh.backend.service.UserService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现 - 使用 MyBatis
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User createUser(User user) {
        if (existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }
        if (user.getEmail() != null && existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + user.getEmail());
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        userMapper.insertUser(user);
        return user;
    }

    @Override
    public User updateUser(Long id, User user) {
        User existingUser = findById(id);

        // 检查用户名是否被其他用户使用
        if (!existingUser.getUsername().equals(user.getUsername()) &&
            existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }

        // 检查邮箱是否被其他用户使用
        if (user.getEmail() != null &&
            !user.getEmail().equals(existingUser.getEmail()) &&
            existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + user.getEmail());
        }

        existingUser.setUsername(user.getUsername());
        existingUser.setEmail(user.getEmail());
        existingUser.setRealName(user.getRealName());
        existingUser.setPhone(user.getPhone());
        existingUser.setAvatarUrl(user.getAvatarUrl());
        existingUser.setStatus(user.getStatus());
        existingUser.setUpdatedAt(LocalDateTime.now());

        // 如果提供了新密码，则更新密码
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        userMapper.updateUser(existingUser);
        return existingUser;
    }

    @Override
    public void deleteUser(Long id) {
        User user = findById(id);
        userMapper.deleteById(id);
    }

    @Override
    public User findById(Long id) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + id);
        }
        return user;
    }

    @Override
    public User findByUsername(String username) {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + username);
        }
        return user;
    }

    @Override
    public User findByUsernameWithRolesAndPermissions(String username) {
        return userMapper.findByUsernameWithRolesAndPermissions(username);
        // 返回null而不是抛出异常，用于认证过滤器
    }

    @Override
    public User findByEmail(String email) {
        User user = userMapper.findByEmail(email);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + email);
        }
        return user;
    }

    @Override
    public User findByUsernameOrEmail(String usernameOrEmail) {
        // 先尝试用户名查找
        User user = userMapper.findByUsername(usernameOrEmail);
        if (user == null) {
            // 再尝试邮箱查找
            user = userMapper.findByEmail(usernameOrEmail);
        }
        if (user == null) {
            throw new RuntimeException("用户不存在: " + usernameOrEmail);
        }
        return user;
    }

    @Override
    public List<User> findAll() {
        return userRepository.findAll();
    }

    @Override
    public Page<User> findByKeyword(String keyword, Pageable pageable) {
        return userRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public void updateUserStatus(Long id, User.UserStatus status) {
        User user = findById(id);
        user.setStatus(status);
        userRepository.save(user);
    }

    @Override
    public void resetPassword(Long id, String newPassword) {
        User user = findById(id);
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    @Override
    public void changePassword(Long id, String oldPassword, String newPassword) {
        User user = findById(id);

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    @Override
    public void lockUser(Long id) {
        updateUserStatus(id, User.UserStatus.LOCKED);
    }

    @Override
    public void unlockUser(Long id) {
        updateUserStatus(id, User.UserStatus.ACTIVE);
    }

    @Override
    public void activateUser(Long id) {
        updateUserStatus(id, User.UserStatus.ACTIVE);
    }

    @Override
    public void deactivateUser(Long id) {
        updateUserStatus(id, User.UserStatus.INACTIVE);
    }

    @Override
    public void deleteUsers(List<Long> ids) {
        userRepository.deleteAllById(ids);
    }

    @Override
    public UserStatistics getUserStatistics() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByStatus(User.UserStatus.ACTIVE);
        long lockedUsers = userRepository.countByStatus(User.UserStatus.LOCKED);
        long inactiveUsers = userRepository.countByStatus(User.UserStatus.INACTIVE);

        return new UserStatistics(totalUsers, activeUsers, lockedUsers, inactiveUsers);
    }

    @Override
    public List<User> findByRoleCode(String roleCode) {
        return userRepository.findByRoleCode(roleCode);
    }

    @Override
    public List<User> findByPermissionCode(String permissionCode) {
        return userRepository.findByPermissionCode(permissionCode);
    }

    @Override
    public void updateLastLoginTime(Long id) {
        User user = findById(id);
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
    }
}
