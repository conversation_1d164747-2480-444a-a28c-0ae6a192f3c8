package zh.backend.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zh.backend.entity.Permission;
import zh.backend.entity.UserPermission;

import java.util.List;

/**
 * 用户权限 MyBatis Mapper
 */
@Mapper
public interface UserPermissionMapper {

    /**
     * 根据用户ID查找激活的权限
     */
    List<Permission> findActivePermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找激活的用户权限关联
     */
    List<UserPermission> findActiveByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和权限类型查找用户权限关联
     */
    List<UserPermission> findByUserIdAndPermissionType(
            @Param("userId") Long userId, 
            @Param("permissionType") String permissionType
    );

    /**
     * 检查用户是否拥有指定权限
     */
    boolean existsByUserIdAndPermissionId(
            @Param("userId") Long userId, 
            @Param("permissionId") Long permissionId
    );

    /**
     * 统计用户的直接权限数量
     */
    long countActivePermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查找即将过期的用户权限
     */
    List<UserPermission> findExpiredPermissions();

    /**
     * 根据用户ID和权限ID查找用户权限关联
     */
    UserPermission findByUserIdAndPermissionId(
            @Param("userId") Long userId, 
            @Param("permissionId") Long permissionId
    );

    /**
     * 根据用户ID查找所有用户权限关联
     */
    List<UserPermission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据权限ID查找所有用户权限关联
     */
    List<UserPermission> findByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 插入用户权限关联
     */
    int insertUserPermission(UserPermission userPermission);

    /**
     * 更新用户权限关联
     */
    int updateUserPermission(UserPermission userPermission);

    /**
     * 根据用户ID删除所有用户权限关联
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据权限ID删除所有用户权限关联
     */
    int deleteByPermissionId(@Param("permissionId") Long permissionId);
}
