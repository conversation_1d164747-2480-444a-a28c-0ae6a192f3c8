package zh.backend.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zh.backend.annotation.RequirePermission;
import zh.backend.dto.*;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.service.RoleService;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/roles")
@RequirePermission("ROLE_MANAGEMENT")
@Validated
public class RoleController {
    
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RoleController.class);

    @Autowired
    private RoleService roleService;

    /**
     * 获取角色列表（支持分页、排序和搜索）
     */
    @GetMapping
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<Page<RoleDTO>>> getRoles(
            @RequestParam(name = "keyword", defaultValue = "") String keyword,
            @RequestParam(name = "status", required = false) Role.RoleStatus status,
            @RequestParam(name = "isSystem", required = false) Boolean isSystem,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "sortBy", defaultValue = "sortOrder") String sortBy,
            @RequestParam(name = "sortDir", defaultValue = "asc") String sortDir) {
        
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Role> roles = roleService.findByKeyword(keyword, status, isSystem, pageable);
            Page<RoleDTO> roleDTOs = roles.map(RoleDTO::new);
            
            return ResponseEntity.ok(ApiResponse.success(roleDTOs));
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取所有激活的角色
     */
    @GetMapping("/active")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<List<RoleDTO>>> getActiveRoles() {
        try {
            List<Role> roles = roleService.findActiveRoles();
            List<RoleDTO> roleDTOs = roles.stream()
                    .map(RoleDTO::new)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(ApiResponse.success(roleDTOs));
        } catch (Exception e) {
            log.error("获取激活角色列表失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 根据ID获取角色
     */
    @GetMapping("/{id}")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<RoleDTO>> getRole(@PathVariable Long id) {
        try {
            Role role = roleService.findById(id);
            return ResponseEntity.ok(ApiResponse.success(new RoleDTO(role)));
        } catch (Exception e) {
            log.error("获取角色详情失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建角色
     */
    @PostMapping
    @RequirePermission("ROLE_CREATE")
    public ResponseEntity<ApiResponse<RoleDTO>> createRole(@Valid @RequestBody RoleCreateRequest request) {
        try {
            Role role = request.toEntity();
            Role createdRole = roleService.createRole(role);
            
            // 如果提供了权限ID列表，则分配权限
            if (request.getPermissionIds() != null && !request.getPermissionIds().isEmpty()) {
                roleService.assignPermissionsToRole(createdRole.getId(), request.getPermissionIds());
                createdRole = roleService.findById(createdRole.getId()); // 重新加载以包含权限
            }
            
            return ResponseEntity.ok(ApiResponse.success(new RoleDTO(createdRole)));
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @RequirePermission("ROLE_UPDATE")
    public ResponseEntity<ApiResponse<RoleDTO>> updateRole(
            @PathVariable Long id, 
            @Valid @RequestBody RoleUpdateRequest request) {
        try {
            Role role = roleService.findById(id);
            request.updateEntity(role);
            Role updatedRole = roleService.updateRole(id, role);
            
            // 如果提供了权限ID列表，则更新权限
            if (request.getPermissionIds() != null) {
                roleService.assignPermissionsToRole(id, request.getPermissionIds());
                updatedRole = roleService.findById(id); // 重新加载以包含权限
            }
            
            return ResponseEntity.ok(ApiResponse.success(new RoleDTO(updatedRole)));
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @RequirePermission("ROLE_DELETE")
    public ResponseEntity<ApiResponse<Void>> deleteRole(@PathVariable Long id) {
        try {
            roleService.deleteRole(id);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @RequirePermission("ROLE_DELETE")
    public ResponseEntity<ApiResponse<Void>> deleteRoles(@RequestBody List<Long> ids) {
        try {
            roleService.deleteRoles(ids);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("批量删除角色失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 分配角色权限
     */
    @PostMapping("/permissions")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<ApiResponse<Void>> assignPermissions(
            @Valid @RequestBody RolePermissionAssignRequest request) {
        try {
            if (request.getReplaceAll()) {
                roleService.assignPermissionsToRole(request.getRoleId(), request.getPermissionIds());
            } else {
                Role role = roleService.findById(request.getRoleId());
                List<Long> currentPermissionIds = role.getPermissions().stream()
                        .map(Permission::getId)
                        .collect(Collectors.toList());
                currentPermissionIds.addAll(request.getPermissionIds());
                roleService.assignPermissionsToRole(request.getRoleId(), currentPermissionIds);
            }
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("分配角色权限失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 移除角色权限
     */
    @DeleteMapping("/{id}/permissions")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<ApiResponse<Void>> removePermissions(
            @PathVariable Long id, 
            @RequestBody List<Long> permissionIds) {
        try {
            roleService.removePermissionsFromRole(id, permissionIds);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("移除角色权限失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取角色权限
     */
    @GetMapping("/{id}/permissions")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<List<Permission>>> getRolePermissions(@PathVariable Long id) {
        try {
            List<Permission> permissions = roleService.getRolePermissions(id);
            return ResponseEntity.ok(ApiResponse.success(permissions));
        } catch (Exception e) {
            log.error("获取角色权限失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 复制角色权限
     */
    @PostMapping("/{sourceId}/copy-to/{targetId}")
    @RequirePermission("ROLE_ASSIGN_PERMISSIONS")
    public ResponseEntity<ApiResponse<Void>> copyRolePermissions(
            @PathVariable Long sourceId, 
            @PathVariable Long targetId) {
        try {
            roleService.copyRolePermissions(sourceId, targetId);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("复制角色权限失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取角色统计信息
     */
    @GetMapping("/statistics")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<RoleStatisticsDTO>> getRoleStatistics() {
        try {
            RoleService.RoleStatistics stats = roleService.getRoleStatistics();
            RoleStatisticsDTO statsDTO = new RoleStatisticsDTO(
                stats.getTotalRoles(),
                stats.getActiveRoles(),
                stats.getTotalRoles() - stats.getActiveRoles(),
                stats.getSystemRoles(),
                stats.getCustomRoles(),
                0L, // TODO: Implement total users count
                0L  // TODO: Implement total permissions count
            );
            return ResponseEntity.ok(ApiResponse.success(statsDTO));
        } catch (Exception e) {
            log.error("获取角色统计信息失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 检查角色编码是否存在
     */
    @GetMapping("/exists/code")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = roleService.existsByCode(code);
            return ResponseEntity.ok(ApiResponse.success(exists));
        } catch (Exception e) {
            log.error("检查角色编码失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 检查角色名称是否存在
     */
    @GetMapping("/exists/name")
    @RequirePermission("ROLE_LIST")
    public ResponseEntity<ApiResponse<Boolean>> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = roleService.existsByName(name);
            return ResponseEntity.ok(ApiResponse.success(exists));
        } catch (Exception e) {
            log.error("检查角色名称失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
