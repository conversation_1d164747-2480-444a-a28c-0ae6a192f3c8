<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限树测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
        }
        .success {
            color: #155724;
            background: #d4edda;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>权限管理功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 测试权限树API</div>
            <button onclick="testPermissionTree()">测试权限树</button>
            <div id="tree-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 测试权限编码检查</div>
            <button onclick="testCodeExists()">测试编码检查</button>
            <div id="code-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 测试权限列表</div>
            <button onclick="testPermissionList()">测试权限列表</button>
            <div id="list-result" class="result"></div>
        </div>
    </div>

    <script>
        // 模拟API调用
        const API_BASE = 'http://localhost:8080/api';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        // 这里需要添加实际的认证token
                        'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }
        
        async function testPermissionTree() {
            const resultDiv = document.getElementById('tree-result');
            resultDiv.textContent = '正在测试权限树API...';
            
            try {
                const response = await makeRequest(`${API_BASE}/permissions/tree`);
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功获取权限树:\n${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `权限树API测试失败:\n${error.message}`;
            }
        }
        
        async function testCodeExists() {
            const resultDiv = document.getElementById('code-result');
            resultDiv.textContent = '正在测试权限编码检查...';
            
            try {
                const response = await makeRequest(`${API_BASE}/permissions/exists?code=USER_MANAGEMENT`);
                resultDiv.className = 'result success';
                resultDiv.textContent = `编码检查结果:\n${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `编码检查测试失败:\n${error.message}`;
            }
        }
        
        async function testPermissionList() {
            const resultDiv = document.getElementById('list-result');
            resultDiv.textContent = '正在测试权限列表...';
            
            try {
                const response = await makeRequest(`${API_BASE}/permissions?page=0&size=5`);
                resultDiv.className = 'result success';
                resultDiv.textContent = `权限列表结果:\n${JSON.stringify(response, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `权限列表测试失败:\n${error.message}`;
            }
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log('权限管理测试页面已加载');
            console.log('请确保后端服务正在运行在 http://localhost:8080');
            console.log('请在浏览器开发者工具中查看详细的网络请求');
        };
    </script>
</body>
</html>
