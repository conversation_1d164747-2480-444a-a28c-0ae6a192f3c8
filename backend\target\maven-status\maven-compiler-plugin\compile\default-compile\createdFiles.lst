zh\backend\repository\UserPermissionRepository.class
zh\backend\annotation\RequirePermission$PermissionMode.class
zh\backend\aspect\PermissionAspect.class
zh\backend\dto\UserPermissionDTO.class
zh\backend\entity\User$UserStatus.class
zh\backend\dto\RolePermissionAssignRequest.class
zh\backend\dto\PermissionDTO.class
zh\backend\config\CacheConfig.class
zh\backend\entity\Permission$PermissionStatus.class
zh\backend\entity\BaseEntity.class
zh\backend\service\impl\UserServiceImpl.class
zh\backend\dto\RoleTemplateDTO.class
zh\backend\controller\PermissionController.class
zh\backend\service\UserService$UserStatistics.class
zh\backend\service\UserService.class
zh\backend\config\JpaConfig.class
zh\backend\controller\ProfileController$ProfileUpdateRequest.class
zh\backend\service\impl\RoleServiceImpl.class
zh\backend\dto\RoleStatisticsDTO.class
zh\backend\entity\PermissionPolicy.class
zh\backend\annotation\RequireRole.class
zh\backend\dto\RoleDTO.class
zh\backend\entity\PermissionPolicy$PolicyStatus.class
zh\backend\repository\RoleRepository.class
zh\backend\entity\Permission.class
zh\backend\controller\AuthController.class
zh\backend\entity\Role.class
zh\backend\entity\User.class
zh\backend\filter\JwtAuthenticationFilter.class
zh\backend\config\DataInitializer.class
zh\backend\controller\UserController.class
zh\backend\entity\Role$RoleStatus.class
zh\backend\service\impl\PermissionServiceImpl.class
zh\backend\service\PermissionService.class
zh\backend\controller\ProfileController$PasswordChangeRequest.class
zh\backend\config\SecurityConfig.class
zh\backend\controller\RoleController.class
zh\backend\entity\Permission$HttpMethod.class
zh\backend\dto\PermissionTreeDTO.class
zh\backend\service\RoleService$RoleStatistics.class
zh\backend\repository\UserRepository.class
zh\backend\dto\RoleCreateRequest.class
zh\backend\entity\Permission$ResourceType.class
zh\backend\controller\HealthController.class
zh\backend\entity\PermissionLog.class
zh\backend\entity\PermissionPolicy$PolicyEffect.class
zh\backend\controller\ProfileController.class
zh\backend\service\RoleService.class
zh\backend\repository\PermissionRepository.class
zh\backend\util\JwtUtil.class
zh\backend\entity\UserPermission$PermissionType.class
zh\backend\BackendApplication.class
zh\backend\entity\UserPermission.class
zh\backend\annotation\RequirePermission.class
zh\backend\entity\UserPermission$UserPermissionStatus.class
zh\backend\dto\RoleUpdateRequest.class
zh\backend\controller\TestController.class
zh\backend\annotation\RequireRole$RoleMode.class
zh\backend\dto\ApiResponse.class
zh\backend\entity\PermissionLog$OperationType.class
zh\backend\GeneratePassword.class
