package zh.backend.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

/**
 * 用户权限关联实体（用于直接给用户分配权限）
 */
@Entity
@Table(name = "user_permissions")
public class UserPermission extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "permission_id", nullable = false)
    private Permission permission;

    @Enumerated(EnumType.STRING)
    @Column(name = "permission_type", nullable = false)
    private PermissionType permissionType = PermissionType.GRANT;

    @Column(name = "granted_at", nullable = false)
    private LocalDateTime grantedAt = LocalDateTime.now();

    @Column(name = "granted_by")
    private Long grantedBy;

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserPermissionStatus status = UserPermissionStatus.ACTIVE;

    // 构造函数
    public UserPermission() {}

    public UserPermission(User user, Permission permission, PermissionType permissionType) {
        this.user = user;
        this.permission = permission;
        this.permissionType = permissionType;
        this.grantedAt = LocalDateTime.now();
    }

    // 权限类型枚举
    public enum PermissionType {
        GRANT("授予"),
        DENY("拒绝");

        private final String description;

        PermissionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 用户权限状态枚举
    public enum UserPermissionStatus {
        ACTIVE("激活"),
        EXPIRED("过期"),
        REVOKED("撤销");

        private final String description;

        UserPermissionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Getters and Setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Permission getPermission() {
        return permission;
    }

    public void setPermission(Permission permission) {
        this.permission = permission;
    }

    public PermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(PermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public LocalDateTime getGrantedAt() {
        return grantedAt;
    }

    public void setGrantedAt(LocalDateTime grantedAt) {
        this.grantedAt = grantedAt;
    }

    public Long getGrantedBy() {
        return grantedBy;
    }

    public void setGrantedBy(Long grantedBy) {
        this.grantedBy = grantedBy;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public UserPermissionStatus getStatus() {
        return status;
    }

    public void setStatus(UserPermissionStatus status) {
        this.status = status;
    }

    // 便捷方法
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isActive() {
        return status == UserPermissionStatus.ACTIVE && !isExpired();
    }

    @Override
    public String toString() {
        return "UserPermission{" +
                "id=" + getId() +
                ", user=" + (user != null ? user.getUsername() : null) +
                ", permission=" + (permission != null ? permission.getCode() : null) +
                ", permissionType=" + permissionType +
                ", status=" + status +
                '}';
    }
}
