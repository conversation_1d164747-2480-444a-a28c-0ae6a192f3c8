package zh.backend.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 权限策略实体（用于动态权限规则）
 */
@Entity
@Table(name = "permission_policies")
public class PermissionPolicy extends BaseEntity {

    @NotBlank(message = "策略名称不能为空")
    @Size(max = 100, message = "策略名称长度不能超过100个字符")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @NotBlank(message = "资源匹配模式不能为空")
    @Column(name = "resource_pattern", nullable = false)
    private String resourcePattern;

    @Column(name = "condition_expression", columnDefinition = "TEXT")
    private String conditionExpression;

    @Enumerated(EnumType.STRING)
    @Column(name = "effect", nullable = false)
    private PolicyEffect effect = PolicyEffect.ALLOW;

    @Column(name = "priority")
    private Integer priority = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PolicyStatus status = PolicyStatus.ACTIVE;

    // 构造函数
    public PermissionPolicy() {}

    public PermissionPolicy(String name, String resourcePattern, PolicyEffect effect) {
        this.name = name;
        this.resourcePattern = resourcePattern;
        this.effect = effect;
    }

    // 策略效果枚举
    public enum PolicyEffect {
        ALLOW("允许"),
        DENY("拒绝");

        private final String description;

        PolicyEffect(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 策略状态枚举
    public enum PolicyStatus {
        ACTIVE("激活"),
        INACTIVE("未激活");

        private final String description;

        PolicyStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getResourcePattern() {
        return resourcePattern;
    }

    public void setResourcePattern(String resourcePattern) {
        this.resourcePattern = resourcePattern;
    }

    public String getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(String conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public PolicyEffect getEffect() {
        return effect;
    }

    public void setEffect(PolicyEffect effect) {
        this.effect = effect;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public PolicyStatus getStatus() {
        return status;
    }

    public void setStatus(PolicyStatus status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PermissionPolicy{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", resourcePattern='" + resourcePattern + '\'' +
                ", effect=" + effect +
                ", priority=" + priority +
                ", status=" + status +
                '}';
    }
}
