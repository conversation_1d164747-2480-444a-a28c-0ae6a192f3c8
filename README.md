# 动态权限管理系统

一个基于Spring Boot 3.3.5 + Vue 3的现代化动态权限管理系统，支持RBAC权限模型、细粒度权限控制和动态权限分配。

## 📋 项目概述

### 🎯 核心特性

- **🔐 完整的RBAC权限模型** - 用户、角色、权限三层权限体系
- **🎨 现代化UI界面** - 基于Vue 3 + Element Plus的响应式界面
- **⚡ 高性能后端** - Spring Boot 3.3.5 + Java 17 + MySQL
- **🔄 动态权限分配** - 支持运行时权限变更，无需重启
- **📊 细粒度权限控制** - 支持菜单、按钮、API、数据级权限
- **🚀 开箱即用** - 完整的开发和部署文档

### 🏗️ 技术架构

#### 后端技术栈
- **框架**: Spring Boot 3.3.5
- **语言**: Java 17
- **数据库**: MySQL 8.0 / H2 (开发)
- **安全**: Spring Security 6.x
- **ORM**: Spring Data JPA + Hibernate
- **缓存**: Spring Cache
- **文档**: OpenAPI 3.0

#### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **HTTP客户端**: Axios

## 🚀 快速开始

### 环境要求

- **Java**: 17+
- **Node.js**: 16+
- **MySQL**: 8.0+ (可选，支持H2)
- **Maven**: 3.6+

### 一键启动

#### 1. 克隆项目
```bash
git clone <repository-url>
cd dynamic-permission-system
```

#### 2. 启动后端
```bash
cd backend
# Windows
start.bat

# Linux/Mac
./start.sh
```

#### 3. 启动前端
```bash
cd frontend
# Windows
start.bat

# Linux/Mac
npm install && npm run dev
```

#### 4. 访问系统
- **前端地址**: http://localhost:5173
- **后端API**: http://localhost:8080
- **默认账户**: admin / admin123

## 📁 项目结构

```
dynamic-permission-system/
├── backend/                    # 后端项目
│   ├── src/main/java/zh/backend/
│   │   ├── annotation/         # 权限注解
│   │   ├── aspect/            # AOP切面
│   │   ├── config/            # 配置类
│   │   ├── controller/        # 控制器
│   │   ├── entity/            # 实体类
│   │   ├── repository/        # 数据访问层
│   │   ├── service/           # 业务逻辑层
│   │   └── BackendApplication.java
│   ├── src/main/resources/
│   │   ├── application.yml    # 配置文件
│   │   └── db/migration/      # 数据库迁移
│   ├── pom.xml               # Maven配置
│   ├── start.bat             # Windows启动脚本
│   └── README.md             # 后端文档
├── frontend/                  # 前端项目
│   ├── src/
│   │   ├── api/              # API服务
│   │   ├── components/       # 公共组件
│   │   ├── directives/       # 自定义指令
│   │   ├── router/           # 路由配置
│   │   ├── stores/           # 状态管理
│   │   ├── utils/            # 工具函数
│   │   ├── views/            # 页面组件
│   │   └── main.js           # 入口文件
│   ├── package.json          # 依赖配置
│   ├── start.bat             # Windows启动脚本
│   └── README.md             # 前端文档
└── README.md                 # 项目总览
```

## 🔐 权限系统设计

### 权限模型

```
用户 (User) ←→ 角色 (Role) ←→ 权限 (Permission)
     ↓              ↓              ↓
  用户信息        角色定义        权限定义
  状态管理        权限分配        资源控制
```

### 权限类型

- **菜单权限** - 控制页面访问
- **按钮权限** - 控制操作按钮显示
- **API权限** - 控制接口访问
- **数据权限** - 控制数据范围

### 权限注解

```java
// 方法级权限控制
@RequirePermission("USER_CREATE")
public void createUser() { }

// 角色级权限控制
@RequireRole("ADMIN")
public void adminOperation() { }

// 多权限控制
@RequirePermission(value = {"USER_CREATE", "USER_UPDATE"}, mode = PermissionMode.ANY)
public void userOperation() { }
```

## 🎨 功能特性

### ✅ 已实现功能

#### 用户管理
- [x] 用户CRUD操作
- [x] 用户状态管理（启用/禁用/锁定）
- [x] 密码重置和修改
- [x] 用户角色分配
- [x] 用户权限查看

#### 角色管理
- [x] 角色CRUD操作
- [x] 角色权限分配
- [x] 角色用户管理
- [x] 系统角色保护

#### 权限管理
- [x] 权限CRUD操作
- [x] 权限树形结构
- [x] 权限状态管理
- [x] 权限分类管理

#### 系统功能
- [x] 用户认证和授权
- [x] 权限缓存机制
- [x] 操作日志记录
- [x] 数据初始化
- [x] 健康检查接口

### 🚧 计划功能

- [ ] 权限策略引擎
- [ ] 数据权限过滤
- [ ] 操作审计日志
- [ ] 权限变更通知
- [ ] 批量权限操作
- [ ] 权限模板管理

## 🛠️ 开发指南

### 后端开发

1. **添加新权限**
   ```java
   @RequirePermission("NEW_PERMISSION")
   @GetMapping("/new-endpoint")
   public ResponseEntity<?> newEndpoint() {
       // 业务逻辑
   }
   ```

2. **权限数据初始化**
   - 在 `DataInitializer` 中添加权限定义
   - 系统启动时自动初始化

3. **自定义权限验证**
   - 实现 `PermissionEvaluator` 接口
   - 在 `PermissionAspect` 中扩展验证逻辑

### 前端开发

1. **权限指令使用**
   ```vue
   <el-button v-can="'USER_CREATE'">创建用户</el-button>
   ```

2. **路由权限配置**
   ```javascript
   {
     path: '/admin/users',
     meta: { requiresPermission: 'USER_LIST' }
   }
   ```

3. **API权限处理**
   ```javascript
   import { hasPermission } from '@/utils/permissions'
   
   if (hasPermission(user, 'USER_CREATE')) {
     // 显示创建按钮
   }
   ```

## 📦 部署指南

### 开发环境

1. **后端配置** (application.yml)
   ```yaml
   spring:
     datasource:
       url: jdbc:h2:mem:testdb
     jpa:
       hibernate:
         ddl-auto: create-drop
   ```

2. **前端配置** (.env.development)
   ```env
   VITE_API_BASE_URL=http://localhost:8080
   ```

### 生产环境

1. **数据库配置**
   ```yaml
   spring:
     datasource:
       url: *********************************************
       username: ${DB_USERNAME}
       password: ${DB_PASSWORD}
   ```

2. **前端构建**
   ```bash
   npm run build
   ```

## 🔍 API文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/{id}` - 更新角色
- `DELETE /api/roles/{id}` - 删除角色

### 权限管理
- `GET /api/permissions` - 获取权限列表
- `GET /api/permissions/tree` - 获取权限树
- `POST /api/permissions` - 创建权限
- `PUT /api/permissions/{id}` - 更新权限

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 文档地址: [Documentation]

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
