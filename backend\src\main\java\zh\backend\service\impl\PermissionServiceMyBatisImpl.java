package zh.backend.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zh.backend.entity.Permission;
import zh.backend.entity.User;
import zh.backend.entity.UserPermission;
import zh.backend.mapper.UserPermissionMapper;
import zh.backend.repository.PermissionRepository;
import zh.backend.repository.UserRepository;
import zh.backend.service.PermissionService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限服务实现类 - 使用 MyBatis
 */
@Service("permissionServiceMyBatis")
public class PermissionServiceMyBatisImpl implements PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserPermissionMapper userPermissionMapper;

    @Override
    public Permission createPermission(Permission permission) {
        return permissionRepository.save(permission);
    }

    @Override
    public Permission updatePermission(Long id, Permission permission) {
        Permission existingPermission = findById(id);
        existingPermission.setName(permission.getName());
        existingPermission.setCode(permission.getCode());
        existingPermission.setDescription(permission.getDescription());
        existingPermission.setResourceType(permission.getResourceType());
        existingPermission.setResourcePath(permission.getResourcePath());
        existingPermission.setHttpMethod(permission.getHttpMethod());
        existingPermission.setParentId(permission.getParentId());
        existingPermission.setLevel(permission.getLevel());
        existingPermission.setSortOrder(permission.getSortOrder());
        existingPermission.setStatus(permission.getStatus());
        return permissionRepository.save(existingPermission);
    }

    @Override
    @Transactional
    public void deletePermission(Long id) {
        // 删除相关的用户权限关联
        userPermissionMapper.deleteByPermissionId(id);
        // 删除权限本身
        permissionRepository.deleteById(id);
    }

    @Override
    public Permission findById(Long id) {
        return permissionRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("权限不存在: " + id));
    }

    @Override
    public Permission findByCode(String code) {
        return permissionRepository.findByCode(code)
            .orElseThrow(() -> new RuntimeException("权限不存在: " + code));
    }

    @Override
    public List<Permission> findAll() {
        return permissionRepository.findAll();
    }

    @Override
    public Page<Permission> findByKeyword(String keyword, Pageable pageable) {
        return permissionRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public List<Permission> buildPermissionTree() {
        return permissionRepository.findTopLevelPermissions();
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#userId")
    public Set<String> getUserPermissions(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        // 重新查询以获取完整的关联数据
        user = userRepository.findByUsernameWithRolesAndPermissions(user.getUsername())
            .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));

        return getUserPermissions(user);
    }

    @Override
    public Set<String> getUserPermissions(User user) {
        System.out.println("[Debug] Calculating permissions for user: " + user.getUsername());
        Set<String> permissions = new HashSet<>();

        // 获取角色权限 - 使用Repository方法避免懒加载问题
        List<Permission> rolePermissions = getUserRolePermissions(user.getId());
        System.out.println("[Debug] Role permissions found: " + rolePermissions.size());
        rolePermissions.forEach(permission -> {
            if (permission.getStatus() == Permission.PermissionStatus.ACTIVE) {
                permissions.add(permission.getCode());
                System.out.println("[Debug] Added permission from role: " + permission.getCode());
            } else {
                System.out.println("[Debug] Skipped inactive permission from role: " + permission.getCode());
            }
        });

        // 获取用户权限关联信息以处理GRANT/DENY类型 - 使用 MyBatis
        List<UserPermission> userPermissions = userPermissionMapper.findByUserId(user.getId());
        userPermissions.forEach(userPermission -> {
            if (userPermission != null && userPermission.getPermission() != null && userPermission.isActive()) {
                System.out.println("[Debug] Processing direct permission entry: " + userPermission.getPermission().getCode() + " Type: " + userPermission.getPermissionType() + " Active: " + userPermission.isActive());
                if (userPermission.getPermissionType() == UserPermission.PermissionType.GRANT) {
                    permissions.add(userPermission.getPermission().getCode());
                    System.out.println("[Debug] Added direct permission (GRANT): " + userPermission.getPermission().getCode());
                } else {
                    // DENY类型权限，从权限集合中移除
                    boolean removed = permissions.remove(userPermission.getPermission().getCode());
                    System.out.println("[Debug] Removed direct permission (DENY): " + userPermission.getPermission().getCode() + " (Removed: " + removed + ")");
                }
            }
        });

        System.out.println("[Debug] Final permissions for user " + user.getUsername() + ": " + permissions);
        return permissions;
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        Set<String> userPermissions = getUserPermissions(userId);
        return userPermissions.contains(permissionCode);
    }

    @Override
    public boolean hasPermission(User user, String permissionCode) {
        Set<String> userPermissions = getUserPermissions(user);
        return userPermissions.contains(permissionCode);
    }

    @Override
    public boolean hasAnyPermission(Long userId, String... permissionCodes) {
        Set<String> userPermissions = getUserPermissions(userId);
        return Arrays.stream(permissionCodes)
            .anyMatch(userPermissions::contains);
    }

    @Override
    public boolean hasAllPermissions(Long userId, String... permissionCodes) {
        Set<String> userPermissions = getUserPermissions(userId);
        return Arrays.stream(permissionCodes)
            .allMatch(userPermissions::contains);
    }

    @Override
    public boolean checkResourceAccess(Long userId, String resourcePath, String httpMethod) {
        List<Permission> userPermissions = getUserDirectPermissions(userId);
        return userPermissions.stream()
            .anyMatch(permission -> 
                permission.getResourcePath() != null &&
                permission.getResourcePath().equals(resourcePath) &&
                (permission.getHttpMethod() == null || permission.getHttpMethod().toString().equals(httpMethod))
            );
    }

    @Override
    @CacheEvict(value = "userPermissions", key = "#userId")
    @Transactional
    public void grantPermissionToUser(Long userId, Long permissionId, Long grantedBy) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
        Permission permission = findById(permissionId);

        // 检查是否已存在 - 使用 MyBatis
        UserPermission existing = userPermissionMapper.findByUserIdAndPermissionId(userId, permissionId);
        
        if (existing != null) {
            // 如果已存在，更新状态和类型
            existing.setPermissionType(UserPermission.PermissionType.GRANT);
            existing.setStatus(UserPermission.UserPermissionStatus.ACTIVE);
            existing.setGrantedBy(grantedBy);
            existing.setGrantedAt(LocalDateTime.now());
            existing.setUpdatedAt(LocalDateTime.now());
            existing.setUpdatedBy(grantedBy);
            userPermissionMapper.updateUserPermission(existing);
        } else {
            // 创建新的权限关联
            UserPermission userPermission = new UserPermission(user, permission, UserPermission.PermissionType.GRANT);
            userPermission.setGrantedBy(grantedBy);
            userPermission.setCreatedAt(LocalDateTime.now());
            userPermission.setUpdatedAt(LocalDateTime.now());
            userPermission.setCreatedBy(grantedBy);
            userPermission.setUpdatedBy(grantedBy);
            userPermissionMapper.insertUserPermission(userPermission);
        }
    }

    @Override
    @CacheEvict(value = "userPermissions", key = "#userId")
    @Transactional
    public void revokePermissionFromUser(Long userId, Long permissionId) {
        UserPermission userPermission = userPermissionMapper.findByUserIdAndPermissionId(userId, permissionId);
        if (userPermission != null) {
            userPermission.setStatus(UserPermission.UserPermissionStatus.REVOKED);
            userPermission.setUpdatedAt(LocalDateTime.now());
            userPermissionMapper.updateUserPermission(userPermission);
        }
    }

    @Override
    public List<Permission> getUserDirectPermissions(Long userId) {
        return userPermissionMapper.findActivePermissionsByUserId(userId);
    }

    @Override
    public List<Permission> getUserRolePermissions(Long userId) {
        return permissionRepository.findByUserId(userId);
    }

    @Override
    @CacheEvict(value = "userPermissions", key = "#userId")
    public void refreshUserPermissionCache(Long userId) {
        // 缓存会自动刷新
    }

    @Override
    @CacheEvict(value = "userPermissions", allEntries = true)
    public void clearPermissionCache() {
        // 清除所有权限缓存
    }

    @Override
    public List<Permission> findByResourceType(Permission.ResourceType resourceType) {
        return permissionRepository.findByResourceType(resourceType);
    }

    @Override
    public List<Permission> findByParentId(Long parentId) {
        return permissionRepository.findByParentId(parentId);
    }

    @Override
    public boolean existsByCode(String code) {
        return permissionRepository.existsByCode(code);
    }

    @Override
    @Transactional
    public void deletePermissions(List<Long> ids) {
        for (Long id : ids) {
            deletePermission(id);
        }
    }
}
