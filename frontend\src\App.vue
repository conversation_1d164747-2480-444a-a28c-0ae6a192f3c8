<template>
  <el-container style="height: 100vh;">
    <!-- 侧栏导航 -->
    <AppNavbar />

    <!-- 主内容区域 -->
    <el-container direction="vertical">
      <!-- 顶部导航栏 -->
      <AppHeader />

      <!-- 主内容 -->
      <el-main style="background-color: #f9fafb; padding: 20px;">
        <el-scrollbar>
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-scrollbar>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { RouterView, useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { onMounted } from 'vue';
import AppNavbar from '@/components/AppNavbar.vue';
import AppHeader from '@/components/AppHeader.vue';

// Authentication and Router
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();

onMounted(() => {
  authStore.initializeAuth();
  
  // 页面加载时，如果是首页则重定向到仪表盘
  if (route.path === '/' && authStore.isAuthenticated) {
    router.replace('/dashboard');
  }
});
</script>

<style>
/* Global styles */
/* Base layout adjustments */
.el-main {
  background-color: #f8f9fa;
  padding: 24px;
  overflow-y: auto;
}

.el-main .el-scrollbar__view {
  height: 100%;
}

/* Router Transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hidden.md\:block {
    display: none !important;
  }
}

/* Utility classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.flex-grow {
  flex-grow: 1;
}

.overflow-hidden {
  overflow: hidden;
}

.ml-3 {
  margin-left: 0.75rem;
}

.flex-1 {
  flex: 1;
}

.min-w-0 {
  min-width: 0;
}

/* Fix for badges */
.item {
  margin-top: 10px;
  margin-right: 40px;
}

/* Ensure proper icon sizes */
.h-5 {
  height: 1.25rem;
}

.w-5 {
  width: 1.25rem;
}
</style>
