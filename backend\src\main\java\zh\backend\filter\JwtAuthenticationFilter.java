package zh.backend.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import zh.backend.entity.User;
import zh.backend.service.UserService;
import zh.backend.service.PermissionService;
import zh.backend.util.JwtUtil;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt)) {
                String username = jwtUtil.getUsernameFromToken(jwt);
                
                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 验证token
                    if (jwtUtil.validateToken(jwt, username)) {
                        // 获取用户信息
                        User user = userService.findByUsernameWithRolesAndPermissions(username);
                        
                        if (user != null && user.getStatus() == User.UserStatus.ACTIVE) {
                            // 获取用户权限
                            Set<String> permissions = permissionService.getUserPermissions(user);
                            
                            // 创建权限列表
                            List<SimpleGrantedAuthority> authorities = permissions.stream()
                                .map(permission -> new SimpleGrantedAuthority("PERMISSION_" + permission))
                                .collect(Collectors.toList());
                            
                            // 添加角色权限
                            user.getRoles().forEach(role -> {
                                authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getCode()));
                            });
                            
                            // 创建认证对象
                            UsernamePasswordAuthenticationToken authentication = 
                                new UsernamePasswordAuthenticationToken(username, null, authorities);
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            
                            // 设置到安全上下文
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            
                            System.out.println("JWT认证成功: 用户 " + username + " 权限: " + permissions);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("无法设置用户认证", ex);
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取JWT token
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
