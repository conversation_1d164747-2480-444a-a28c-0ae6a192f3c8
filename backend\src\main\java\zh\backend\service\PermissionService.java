package zh.backend.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import zh.backend.entity.Permission;
import zh.backend.entity.User;

import java.util.List;
import java.util.Set;

/**
 * 权限服务接口
 */
public interface PermissionService {

    /**
     * 创建权限
     */
    Permission createPermission(Permission permission);

    /**
     * 更新权限
     */
    Permission updatePermission(Long id, Permission permission);

    /**
     * 删除权限
     */
    void deletePermission(Long id);

    /**
     * 根据ID查找权限
     */
    Permission findById(Long id);

    /**
     * 根据编码查找权限
     */
    Permission findByCode(String code);

    /**
     * 查找所有权限
     */
    List<Permission> findAll();

    /**
     * 分页查询权限
     */
    Page<Permission> findByKeyword(String keyword, Pageable pageable);

    /**
     * 构建权限树
     */
    List<Permission> buildPermissionTree();

    /**
     * 根据用户ID获取用户权限
     */
    Set<String> getUserPermissions(Long userId);

    /**
     * 根据用户获取用户权限
     */
    Set<String> getUserPermissions(User user);

    /**
     * 检查用户是否拥有指定权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否拥有指定权限
     */
    boolean hasPermission(User user, String permissionCode);

    /**
     * 检查用户是否拥有任意一个权限
     */
    boolean hasAnyPermission(Long userId, String... permissionCodes);

    /**
     * 检查用户是否拥有所有权限
     */
    boolean hasAllPermissions(Long userId, String... permissionCodes);

    /**
     * 检查资源访问权限
     */
    boolean checkResourceAccess(Long userId, String resourcePath, String httpMethod);

    /**
     * 为用户直接分配权限
     */
    void grantPermissionToUser(Long userId, Long permissionId, Long grantedBy);

    /**
     * 撤销用户的直接权限
     */
    void revokePermissionFromUser(Long userId, Long permissionId);

    /**
     * 获取用户的直接权限
     */
    List<Permission> getUserDirectPermissions(Long userId);

    /**
     * 获取用户通过角色获得的权限
     */
    List<Permission> getUserRolePermissions(Long userId);

    /**
     * 刷新用户权限缓存
     */
    void refreshUserPermissionCache(Long userId);

    /**
     * 清除所有权限缓存
     */
    void clearPermissionCache();

    /**
     * 根据资源类型查找权限
     */
    List<Permission> findByResourceType(Permission.ResourceType resourceType);

    /**
     * 根据父权限ID查找子权限
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 批量删除权限
     */
    void deletePermissions(List<Long> ids);
}
