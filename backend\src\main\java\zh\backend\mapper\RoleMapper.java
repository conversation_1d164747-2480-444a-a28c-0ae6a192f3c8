package zh.backend.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zh.backend.entity.Role;

import java.util.List;

/**
 * 角色 MyBatis Mapper
 */
@Mapper
public interface RoleMapper {

    /**
     * 根据ID查找角色
     */
    Role findById(@Param("id") Long id);

    /**
     * 根据角色编码查找角色
     */
    Role findByCode(@Param("code") String code);

    /**
     * 根据角色名称查找角色
     */
    Role findByName(@Param("name") String name);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByCode(@Param("code") String code);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(@Param("name") String name);

    /**
     * 查找所有角色
     */
    List<Role> findAll();

    /**
     * 根据状态查找角色
     */
    List<Role> findByStatus(@Param("status") String status);

    /**
     * 查找系统内置角色
     */
    List<Role> findByIsSystemTrue();

    /**
     * 查找非系统角色
     */
    List<Role> findByIsSystemFalse();

    /**
     * 分页查询角色（支持关键字搜索）
     */
    List<Role> findByKeyword(
        @Param("keyword") String keyword,
        @Param("offset") int offset,
        @Param("size") int size,
        @Param("sortBy") String sortBy,
        @Param("sortDir") String sortDir
    );

    /**
     * 统计关键字搜索结果数量
     */
    long countByKeyword(@Param("keyword") String keyword);

    /**
     * 根据用户ID查找角色
     */
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * 根据权限ID查找角色
     */
    List<Role> findByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 统计角色数量
     */
    long count();

    /**
     * 根据状态统计角色数量
     */
    long countByStatus(@Param("status") String status);

    /**
     * 插入角色
     */
    int insertRole(Role role);

    /**
     * 更新角色
     */
    int updateRole(Role role);

    /**
     * 根据ID删除角色
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除角色
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 为角色分配权限
     */
    int assignPermissionToRole(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 移除角色的权限
     */
    int removePermissionFromRole(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 清除角色的所有权限
     */
    int clearRolePermissions(@Param("roleId") Long roleId);

    /**
     * 为用户分配角色
     */
    int assignRoleToUser(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 移除用户的角色
     */
    int removeRoleFromUser(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 清除用户的所有角色
     */
    int clearUserRoles(@Param("userId") Long userId);

    /**
     * 批量为用户分配角色
     */
    int batchAssignRolesToUser(@Param("userId") Long userId, @Param("roleIds") List<Long> roleIds);

    /**
     * 批量为角色分配权限
     */
    int batchAssignPermissionsToRole(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);
}
