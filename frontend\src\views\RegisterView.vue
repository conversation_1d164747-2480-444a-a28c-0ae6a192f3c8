<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <div class="logo">
          <h1>📝 用户注册</h1>
          <p>创建您的账户，开始使用我们的服务</p>
        </div>
      </div>

      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label for="username">
            <i class="icon">👤</i>
            用户名
          </label>
          <input
            type="text"
            id="username"
            v-model="username"
            :class="{ 'error': errors.username }"
            placeholder="请输入用户名 (3-20字符)"
            required
            @blur="validateUsername"
            @input="clearError('username')"
            @keydown.enter="focusEmail"
            autocomplete="username"
          />
          <span v-if="errors.username" class="field-error">{{ errors.username }}</span>
          <span v-if="username && !errors.username" class="field-hint">✓ 用户名可用</span>
        </div>

        <div class="form-group">
          <label for="email">
            <i class="icon">📧</i>
            邮箱地址
          </label>
          <input
            type="email"
            id="email"
            v-model="email"
            :class="{ 'error': errors.email }"
            placeholder="请输入邮箱地址"
            required
            @blur="validateEmail"
            @input="clearError('email')"
            @keydown.enter="focusPassword"
            autocomplete="email"
            ref="emailInput"
          />
          <span v-if="errors.email" class="field-error">{{ errors.email }}</span>
          <span v-if="email && !errors.email" class="field-hint">✓ 邮箱格式正确</span>
        </div>

        <div class="form-group">
          <label for="password">
            <i class="icon">🔒</i>
            密码
          </label>
          <div class="password-input">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="password"
              :class="{ 'error': errors.password }"
              placeholder="请输入密码 (至少6位)"
              required
              @blur="validatePassword"
              @input="clearError('password')"
              @keydown.enter="focusConfirmPassword"
              autocomplete="new-password"
              ref="passwordInput"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showPassword = !showPassword"
              :title="showPassword ? '隐藏密码' : '显示密码'"
            >
              <i class="toggle-icon" :class="{ 'visible': showPassword }">
                {{ showPassword ? '🙈' : '👁️' }}
              </i>
            </button>
          </div>
          <span v-if="errors.password" class="field-error">{{ errors.password }}</span>
          <div v-if="password && !errors.password" class="password-strength">
            <div class="strength-meter">
              <div class="strength-bar" :class="passwordStrength.class" :style="{ width: passwordStrength.width }"></div>
            </div>
            <div class="strength-info">
              <span class="strength-text">密码强度: {{ passwordStrength.text }}</span>
              <div v-if="passwordStrength.tips && passwordStrength.tips.length > 0" class="strength-tips">
                <span class="tips-label">建议:</span>
                <ul class="tips-list">
                  <li v-for="tip in passwordStrength.tips.slice(0, 3)" :key="tip">{{ tip }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">
            <i class="icon">🔐</i>
            确认密码
          </label>
          <input
            type="password"
            id="confirmPassword"
            v-model="confirmPassword"
            :class="{ 'error': errors.confirmPassword }"
            placeholder="请再次输入密码"
            required
            @blur="validateConfirmPassword"
            @input="clearError('confirmPassword')"
            @keydown.enter="focusRealName"
            autocomplete="new-password"
            ref="confirmPasswordInput"
          />
          <span v-if="errors.confirmPassword" class="field-error">{{ errors.confirmPassword }}</span>
          <span v-if="confirmPassword && password === confirmPassword && !errors.confirmPassword" class="field-hint">✓ 密码匹配</span>
        </div>

        <div class="form-group">
          <label for="realName">
            <i class="icon">🏷️</i>
            真实姓名 (可选)
          </label>
          <input
            type="text"
            id="realName"
            v-model="realName"
            placeholder="请输入真实姓名"
            @keydown.enter="handleRegister"
            autocomplete="name"
            ref="realNameInput"
          />
        </div>

        <div class="form-group">
          <label class="agreement">
            <input type="checkbox" v-model="agreeToTerms" required>
            <span class="checkmark"></span>
            我已阅读并同意 <a href="#" @click.prevent="showTerms">《用户协议》</a> 和 <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
          </label>
        </div>

        <button
          type="submit"
          class="register-button"
          :disabled="authStore.loading || !isFormValid"
          :class="{ 'loading': authStore.loading }"
        >
          <span v-if="authStore.loading" class="spinner"></span>
          {{ authStore.loading ? '注册中...' : '创建账户' }}
        </button>

        <div v-if="authStore.error" class="error-message">
          <i class="icon">⚠️</i>
          {{ authStore.error }}
          <button type="button" class="error-close" @click="clearAuthError" title="关闭">×</button>
        </div>

        <div v-if="registrationSuccess" class="success-message">
          <i class="icon">🎉</i>
          <div class="success-content">
            <h4>注册成功！</h4>
            <p>欢迎加入我们，{{ countdown }}秒后自动跳转到登录页面</p>
            <div class="success-progress">
              <div class="progress-bar" :style="{ width: `${(4 - countdown) * 33.33}%` }"></div>
            </div>
          </div>
        </div>
      </form>

      <div class="register-footer">
        <p>已有账户？
          <router-link to="/login" class="login-link">立即登录</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const username = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const realName = ref('');
const showPassword = ref(false);
const agreeToTerms = ref(false);
const registrationSuccess = ref(false);
const countdown = ref(3);
const errors = ref({});

// 引用DOM元素
const emailInput = ref(null);
const passwordInput = ref(null);
const confirmPasswordInput = ref(null);
const realNameInput = ref(null);

const authStore = useAuthStore();
const router = useRouter();

// 焦点控制
const focusEmail = () => {
  if (emailInput.value) {
    emailInput.value.focus();
  }
};

const focusPassword = () => {
  if (passwordInput.value) {
    passwordInput.value.focus();
  }
};

const focusConfirmPassword = () => {
  if (confirmPasswordInput.value) {
    confirmPasswordInput.value.focus();
  }
};

const focusRealName = () => {
  if (realNameInput.value) {
    realNameInput.value.focus();
  }
};

// 表单验证
const validateUsername = () => {
  if (!username.value.trim()) {
    errors.value.username = '请输入用户名';
    return false;
  }
  if (username.value.length < 3 || username.value.length > 20) {
    errors.value.username = '用户名长度需要在3-20个字符之间';
    return false;
  }
  if (!/^[a-zA-Z0-9_]+$/.test(username.value)) {
    errors.value.username = '用户名只能包含字母、数字和下划线';
    return false;
  }
  return true;
};

const validateEmail = () => {
  if (!email.value.trim()) {
    errors.value.email = '请输入邮箱地址';
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.value)) {
    errors.value.email = '请输入有效的邮箱地址';
    return false;
  }
  return true;
};

const validatePassword = () => {
  if (!password.value.trim()) {
    errors.value.password = '请输入密码';
    return false;
  }
  if (password.value.length < 6) {
    errors.value.password = '密码长度至少6位';
    return false;
  }
  return true;
};

const validateConfirmPassword = () => {
  if (!confirmPassword.value.trim()) {
    errors.value.confirmPassword = '请确认密码';
    return false;
  }
  if (password.value !== confirmPassword.value) {
    errors.value.confirmPassword = '两次输入的密码不一致';
    return false;
  }
  return true;
};

const clearError = (field) => {
  if (errors.value[field]) {
    delete errors.value[field];
  }
};

const clearAuthError = () => {
  authStore.error = null;
};

// 密码强度检测
const passwordStrength = computed(() => {
  const pwd = password.value;
  if (!pwd) return { width: '0%', class: '', text: '', tips: [] };
  
  let score = 0;
  const tips = [];
  
  // 长度检查
  if (pwd.length >= 6) {
    score++;
  } else {
    tips.push('至少6个字符');
  }
  
  if (pwd.length >= 8) {
    score++;
  } else if (pwd.length >= 6) {
    tips.push('建议8个或更多字符');
  }
  
  // 复杂度检查
  if (/[A-Z]/.test(pwd)) {
    score++;
  } else {
    tips.push('包含大写字母');
  }
  
  if (/[a-z]/.test(pwd)) {
    score++;
  } else {
    tips.push('包含小写字母');
  }
  
  if (/[0-9]/.test(pwd)) {
    score++;
  } else {
    tips.push('包含数字');
  }
  
  if (/[^A-Za-z0-9]/.test(pwd)) {
    score++;
  } else {
    tips.push('包含特殊字符(!@#$%^&*等)');
  }
  
  let result = {};
  if (score <= 2) {
    result = { width: '33%', class: 'weak', text: '弱' };
  } else if (score <= 4) {
    result = { width: '66%', class: 'medium', text: '中等' };
  } else {
    result = { width: '100%', class: 'strong', text: '强' };
  }
  
  result.tips = tips;
  return result;
});

const isFormValid = computed(() => {
  return username.value.trim() &&
         email.value.trim() &&
         password.value.trim() &&
         confirmPassword.value.trim() &&
         password.value === confirmPassword.value &&
         agreeToTerms.value &&
         Object.keys(errors.value).length === 0;
});

// 注册处理
const handleRegister = async () => {
  if (!validateUsername() || !validateEmail() || !validatePassword() || !validateConfirmPassword()) {
    return;
  }

  try {
    const success = await authStore.register({
      username: username.value,
      email: email.value,
      password: password.value,
      realName: realName.value,
    });
    
    if (success) {
      registrationSuccess.value = true;
      startCountdown();
    }
  } catch (error) {
    console.error('注册失败:', error);
  }
};

// 倒计时跳转
const startCountdown = () => {
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
      router.push('/login');
    }
  }, 1000);
};

const showTerms = () => {
  alert('用户协议：请遵守相关法律法规，文明使用本系统。');
};

const showPrivacy = () => {
  alert('隐私政策：我们承诺保护您的个人信息安全。');
};

// 监听密码变化，重新验证确认密码
watch(password, () => {
  if (confirmPassword.value) {
    validateConfirmPassword();
  }
});

// 自动聚焦到用户名输入框
onMounted(() => {
  const usernameInput = document.getElementById('username');
  if (usernameInput) {
    setTimeout(() => {
      usernameInput.focus();
    }, 100);
  }
});
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.register-container {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  border: 1px solid #e5e7eb;
  animation: slideUp 0.5s ease-out;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo h1 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
}

.logo p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.register-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

label {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.icon {
  margin-right: 8px;
  font-size: 16px;
}

input[type="text"],
input[type="password"],
input[type="email"] {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #f8f9fa;
}

input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.field-error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.field-hint {
  color: #28a745;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #666;
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.password-toggle:hover {
  background: rgba(236, 72, 153, 0.1);
  color: #ec4899;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.toggle-icon.visible {
  transform: scale(1.1);
}

.password-strength {
  margin-top: 8px;
}

.strength-meter {
  width: 100%;
  height: 6px;
  background: #e1e5e9;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 6px;
}

.strength-bar {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 3px;
}

.strength-bar.weak {
  background: linear-gradient(90deg, #dc3545, #e57373);
}

.strength-bar.medium {
  background: linear-gradient(90deg, #ffc107, #ffeb3b);
}

.strength-bar.strong {
  background: linear-gradient(90deg, #28a745, #4caf50);
}

.strength-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.strength-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.strength-tips {
  flex: 1;
  text-align: right;
}

.tips-label {
  font-size: 11px;
  color: #999;
  font-weight: 500;
}

.tips-list {
  margin: 2px 0 0 0;
  padding: 0;
  list-style: none;
}

.tips-list li {
  font-size: 10px;
  color: #999;
  line-height: 1.3;
  position: relative;
  padding-left: 8px;
}

.tips-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #ccc;
}

.agreement {
  display: flex !important;
  align-items: flex-start;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  margin: 0 !important;
  line-height: 1.4;
}

.agreement input {
  width: auto !important;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.agreement a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.agreement a:hover {
  text-decoration: underline;
}

.register-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.register-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff3d;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message, .success-message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
}

.error-message {
  background: #fff5f5;
  color: #dc3545;
  border: 1px solid #f5c6cb;
  padding-right: 40px;
}

.error-close {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.error-close:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.success-message {
  background: #f0fff4;
  color: #28a745;
  border: 1px solid #c3e6cb;
  align-items: flex-start;
  padding: 16px;
}

.success-content {
  flex: 1;
  margin-left: 8px;
}

.success-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.success-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  opacity: 0.8;
}

.success-progress {
  width: 100%;
  height: 4px;
  background: rgba(40, 167, 69, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #28a745;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.register-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e1e5e9;
}

.register-footer p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .register-page {
    padding: 16px;
  }
  
  .register-container {
    padding: 32px;
    max-width: 95%;
  }
  
  .logo h1 {
    font-size: 26px;
  }
  
  .strength-info {
    flex-direction: column;
    gap: 6px;
  }
  
  .tips-list li {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .register-page {
    padding: 12px;
    align-items: flex-start;
    padding-top: 20px;
  }
  
  .register-container {
    padding: 24px;
    margin: 0;
    max-width: 100%;
    border-radius: 16px;
  }
  
  .logo h1 {
    font-size: 24px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  input[type="text"],
  input[type="password"],
  input[type="email"] {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 12px 14px;
  }
  
  .register-button {
    padding: 14px;
    font-size: 15px;
  }
  
  .agreement {
    font-size: 13px;
  }
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义复选框样式 */
.agreement input[type="checkbox"] {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #e1e5e9;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.agreement input[type="checkbox"]:checked {
  background: #667eea;
  border-color: #667eea;
}

.agreement input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  font-weight: bold;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.agreement input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
</style>