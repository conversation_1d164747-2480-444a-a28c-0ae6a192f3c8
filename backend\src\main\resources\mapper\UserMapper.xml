<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zh.backend.mapper.UserMapper">

    <!-- User ResultMap -->
    <resultMap id="UserResultMap" type="zh.backend.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="email" column="email"/>
        <result property="password" column="password"/>
        <result property="realName" column="real_name"/>
        <result property="phone" column="phone"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="status" column="status"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <!-- User with Roles ResultMap -->
    <resultMap id="UserWithRolesResultMap" type="zh.backend.entity.User" extends="UserResultMap">
        <collection property="roles" ofType="zh.backend.entity.Role">
            <id property="id" column="role_id"/>
            <result property="name" column="role_name"/>
            <result property="code" column="role_code"/>
            <result property="description" column="role_description"/>
            <result property="status" column="role_status"/>
            <result property="isSystem" column="role_is_system"/>
            <result property="sortOrder" column="role_sort_order"/>
            <result property="createdAt" column="role_created_at"/>
            <result property="updatedAt" column="role_updated_at"/>
            <result property="createdBy" column="role_created_by"/>
            <result property="updatedBy" column="role_updated_by"/>
        </collection>
    </resultMap>

    <!-- 根据ID查找用户 -->
    <select id="findById" resultMap="UserResultMap">
        SELECT * FROM users WHERE id = #{id}
    </select>

    <!-- 根据用户名查找用户 -->
    <select id="findByUsername" resultMap="UserResultMap">
        SELECT * FROM users WHERE username = #{username}
    </select>

    <!-- 根据用户名查找用户（包含角色和权限） -->
    <select id="findByUsernameWithRolesAndPermissions" resultMap="UserWithRolesResultMap">
        SELECT DISTINCT 
            u.*,
            r.id as role_id,
            r.name as role_name,
            r.code as role_code,
            r.description as role_description,
            r.status as role_status,
            r.is_system as role_is_system,
            r.sort_order as role_sort_order,
            r.created_at as role_created_at,
            r.updated_at as role_updated_at,
            r.created_by as role_created_by,
            r.updated_by as role_updated_by
        FROM users u 
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON r.id = ur.role_id
        WHERE u.username = #{username}
    </select>

    <!-- 根据邮箱查找用户 -->
    <select id="findByEmail" resultMap="UserResultMap">
        SELECT * FROM users WHERE email = #{email}
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE email = #{email}
    </select>

    <!-- 查找所有用户 -->
    <select id="findAll" resultMap="UserResultMap">
        SELECT * FROM users ORDER BY created_at DESC
    </select>

    <!-- 根据状态查找用户 -->
    <select id="findByStatus" resultMap="UserResultMap">
        SELECT * FROM users WHERE status = #{status} ORDER BY created_at DESC
    </select>

    <!-- 分页查询用户（支持关键字搜索） -->
    <select id="findByKeyword" resultMap="UserResultMap">
        SELECT DISTINCT u.* FROM users u
        <if test="roleId != null">
            LEFT JOIN user_roles ur ON u.id = ur.user_id
        </if>
        <where>
            <if test="keyword != null and keyword != ''">
                (u.username LIKE CONCAT('%', #{keyword}, '%') 
                OR u.email LIKE CONCAT('%', #{keyword}, '%') 
                OR u.real_name LIKE CONCAT('%', #{keyword}, '%') 
                OR u.phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND u.status = #{status}
            </if>
            <if test="roleId != null">
                AND ur.role_id = #{roleId}
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="sortBy == 'username'">u.username</when>
            <when test="sortBy == 'email'">u.email</when>
            <when test="sortBy == 'realName'">u.real_name</when>
            <when test="sortBy == 'createdAt'">u.created_at</when>
            <when test="sortBy == 'updatedAt'">u.updated_at</when>
            <when test="sortBy == 'lastLoginTime'">u.last_login_time</when>
            <otherwise>u.id</otherwise>
        </choose>
        <choose>
            <when test="sortDir == 'desc'">DESC</when>
            <otherwise>ASC</otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 统计关键字搜索结果数量 -->
    <select id="countByKeyword" resultType="long">
        SELECT COUNT(DISTINCT u.id) FROM users u
        <if test="roleId != null">
            LEFT JOIN user_roles ur ON u.id = ur.user_id
        </if>
        <where>
            <if test="keyword != null and keyword != ''">
                (u.username LIKE CONCAT('%', #{keyword}, '%') 
                OR u.email LIKE CONCAT('%', #{keyword}, '%') 
                OR u.real_name LIKE CONCAT('%', #{keyword}, '%') 
                OR u.phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND u.status = #{status}
            </if>
            <if test="roleId != null">
                AND ur.role_id = #{roleId}
            </if>
        </where>
    </select>

    <!-- 根据角色查找用户 -->
    <select id="findByRoleCode" resultMap="UserResultMap">
        SELECT DISTINCT u.* FROM users u 
        JOIN user_roles ur ON u.id = ur.user_id
        JOIN roles r ON r.id = ur.role_id
        WHERE r.code = #{roleCode}
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据权限查找用户 -->
    <select id="findByPermissionCode" resultMap="UserResultMap">
        SELECT DISTINCT u.* FROM users u 
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON r.id = ur.role_id
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN permissions p1 ON p1.id = rp.permission_id
        LEFT JOIN user_permissions up ON u.id = up.user_id
        LEFT JOIN permissions p2 ON p2.id = up.permission_id
        WHERE p1.code = #{permissionCode} OR p2.code = #{permissionCode}
        ORDER BY u.created_at DESC
    </select>

    <!-- 统计用户数量 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM users
    </select>

    <!-- 根据状态统计用户数量 -->
    <select id="countByStatus" resultType="long">
        SELECT COUNT(*) FROM users WHERE status = #{status}
    </select>

    <!-- 根据角色统计用户数量 -->
    <select id="countByRole" resultType="map">
        SELECT r.name as roleName, COUNT(ur.user_id) as count 
        FROM roles r 
        LEFT JOIN user_roles ur ON r.id = ur.role_id 
        GROUP BY r.id, r.name
        ORDER BY count DESC
    </select>

    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="zh.backend.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            username, email, password, real_name, phone, avatar_url, status,
            last_login_time, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{username}, #{email}, #{password}, #{realName}, #{phone}, #{avatarUrl}, #{status},
            #{lastLoginTime}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 更新用户 -->
    <update id="updateUser" parameterType="zh.backend.entity.User">
        UPDATE users SET
            username = #{username},
            email = #{email},
            real_name = #{realName},
            phone = #{phone},
            avatar_url = #{avatarUrl},
            status = #{status},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById">
        DELETE FROM users WHERE id = #{id}
    </delete>

    <!-- 批量删除用户 -->
    <delete id="deleteByIds">
        DELETE FROM users WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE users SET last_login_time = NOW() WHERE id = #{id}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        UPDATE users SET status = #{status}, updated_at = NOW() WHERE id = #{id}
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE users SET password = #{password}, updated_at = NOW() WHERE id = #{id}
    </update>

</mapper>
