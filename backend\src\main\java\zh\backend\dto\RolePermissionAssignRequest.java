package zh.backend.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 角色权限分配请求DTO
 */
public class RolePermissionAssignRequest {

    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @NotEmpty(message = "权限ID列表不能为空")
    private List<Long> permissionIds;

    private Boolean replaceAll = true; // 是否替换所有权限，false表示追加

    // 构造函数
    public RolePermissionAssignRequest() {}

    public RolePermissionAssignRequest(Long roleId, List<Long> permissionIds) {
        this.roleId = roleId;
        this.permissionIds = permissionIds;
    }

    // Getters and Setters
    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<Long> getPermissionIds() {
        return permissionIds;
    }

    public void setPermissionIds(List<Long> permissionIds) {
        this.permissionIds = permissionIds;
    }

    public Boolean getReplaceAll() {
        return replaceAll;
    }

    public void setReplaceAll(Boolean replaceAll) {
        this.replaceAll = replaceAll;
    }
}
