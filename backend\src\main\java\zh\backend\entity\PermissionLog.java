package zh.backend.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

/**
 * 权限操作日志实体
 */
@Entity
@Table(name = "permission_logs")
public class PermissionLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false)
    private OperationType operationType;

    @Column(name = "resource_path")
    private String resourcePath;

    @Column(name = "http_method", length = 10)
    private String httpMethod;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    @Column(name = "details", columnDefinition = "JSON")
    private String details;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    // 构造函数
    public PermissionLog() {}

    public PermissionLog(User user, OperationType operationType, String resourcePath) {
        this.user = user;
        this.operationType = operationType;
        this.resourcePath = resourcePath;
        this.createdAt = LocalDateTime.now();
    }

    // 操作类型枚举
    public enum OperationType {
        LOGIN("登录"),
        LOGOUT("登出"),
        ACCESS_GRANTED("访问授权"),
        ACCESS_DENIED("访问拒绝"),
        PERMISSION_CHANGED("权限变更");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "PermissionLog{" +
                "id=" + id +
                ", user=" + (user != null ? user.getUsername() : null) +
                ", operationType=" + operationType +
                ", resourcePath='" + resourcePath + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
