<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zh.backend.mapper.PermissionMapper">

    <!-- Permission ResultMap -->
    <resultMap id="PermissionResultMap" type="zh.backend.entity.Permission">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="description" column="description"/>
        <result property="resourceType" column="resource_type"/>
        <result property="resourcePath" column="resource_path"/>
        <result property="httpMethod" column="http_method"/>
        <result property="parentId" column="parent_id"/>
        <result property="level" column="level"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="isSystem" column="is_system"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <!-- 根据ID查找权限 -->
    <select id="findById" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE id = #{id}
    </select>

    <!-- 根据权限编码查找权限 -->
    <select id="findByCode" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE code = #{code}
    </select>

    <!-- 检查权限编码是否存在 -->
    <select id="existsByCode" resultType="boolean">
        SELECT COUNT(*) > 0 FROM permissions WHERE code = #{code}
    </select>

    <!-- 查找所有权限 -->
    <select id="findAll" resultMap="PermissionResultMap">
        SELECT * FROM permissions ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据资源类型查找权限 -->
    <select id="findByResourceType" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE resource_type = #{resourceType}
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据状态查找权限 -->
    <select id="findByStatus" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE status = #{status}
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 查找系统内置权限 -->
    <select id="findByIsSystemTrue" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE is_system = true
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 查找非系统权限 -->
    <select id="findByIsSystemFalse" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE is_system = false
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据父权限ID查找子权限 -->
    <select id="findByParentId" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE parent_id = #{parentId}
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 查找顶级权限（无父权限） -->
    <select id="findTopLevelPermissions" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE parent_id IS NULL
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据层级查找权限 -->
    <select id="findByLevel" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE level = #{level}
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据角色ID查找权限 -->
    <select id="findByRoleId" resultMap="PermissionResultMap">
        SELECT p.* FROM permissions p 
        JOIN role_permissions rp ON p.id = rp.permission_id 
        WHERE rp.role_id = #{roleId}
        ORDER BY p.sort_order ASC, p.name ASC
    </select>

    <!-- 根据用户ID查找权限（包括角色权限和直接权限） -->
    <select id="findByUserId" resultMap="PermissionResultMap">
        SELECT DISTINCT p.* FROM permissions p 
        LEFT JOIN role_permissions rp ON p.id = rp.permission_id
        LEFT JOIN user_roles ur ON rp.role_id = ur.role_id
        LEFT JOIN user_permissions up ON p.id = up.permission_id
        WHERE (ur.user_id = #{userId}) 
           OR (up.user_id = #{userId} AND up.status = 'ACTIVE' AND up.permission_type = 'GRANT')
        ORDER BY p.sort_order ASC, p.name ASC
    </select>

    <!-- 根据资源路径和HTTP方法查找权限 -->
    <select id="findByResourcePathAndHttpMethod" resultMap="PermissionResultMap">
        SELECT * FROM permissions 
        WHERE resource_path = #{resourcePath} 
          AND (http_method = #{httpMethod} OR http_method = 'ALL')
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 分页查询权限（支持关键字搜索） -->
    <select id="findByKeyword" resultMap="PermissionResultMap">
        SELECT * FROM permissions 
        <where>
            <if test="keyword != null and keyword != ''">
                (name LIKE CONCAT('%', #{keyword}, '%') 
                OR code LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%') 
                OR resource_path LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="sortBy == 'name'">name</when>
            <when test="sortBy == 'code'">code</when>
            <when test="sortBy == 'createdAt'">created_at</when>
            <when test="sortBy == 'updatedAt'">updated_at</when>
            <otherwise>id</otherwise>
        </choose>
        <choose>
            <when test="sortDir == 'desc'">DESC</when>
            <otherwise>ASC</otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 统计关键字搜索结果数量 -->
    <select id="countByKeyword" resultType="long">
        SELECT COUNT(*) FROM permissions 
        <where>
            <if test="keyword != null and keyword != ''">
                (name LIKE CONCAT('%', #{keyword}, '%') 
                OR code LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%') 
                OR resource_path LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 构建权限树结构 -->
    <select id="findActivePermissionsForTree" resultMap="PermissionResultMap">
        SELECT * FROM permissions WHERE status = 'ACTIVE' 
        ORDER BY level ASC, sort_order ASC, name ASC
    </select>

    <!-- 统计权限数量 -->
    <select id="countByStatus" resultType="long">
        SELECT COUNT(*) FROM permissions WHERE status = #{status}
    </select>

    <!-- 根据资源类型统计权限数量 -->
    <select id="countByResourceType" resultType="map">
        SELECT resource_type as resourceType, COUNT(*) as count 
        FROM permissions 
        GROUP BY resource_type
    </select>

    <!-- 查找权限及其子权限数量 -->
    <select id="findPermissionsWithChildrenCount" resultType="map">
        SELECT p.*, COUNT(c.id) as childrenCount 
        FROM permissions p 
        LEFT JOIN permissions c ON c.parent_id = p.id 
        GROUP BY p.id
        ORDER BY p.sort_order ASC, p.name ASC
    </select>

    <!-- 插入权限 -->
    <insert id="insertPermission" parameterType="zh.backend.entity.Permission" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO permissions (
            name, code, description, resource_type, resource_path, http_method,
            parent_id, level, sort_order, is_system, status,
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{name}, #{code}, #{description}, #{resourceType}, #{resourcePath}, #{httpMethod},
            #{parentId}, #{level}, #{sortOrder}, #{isSystem}, #{status},
            #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 更新权限 -->
    <update id="updatePermission" parameterType="zh.backend.entity.Permission">
        UPDATE permissions SET
            name = #{name},
            code = #{code},
            description = #{description},
            resource_type = #{resourceType},
            resource_path = #{resourcePath},
            http_method = #{httpMethod},
            parent_id = #{parentId},
            level = #{level},
            sort_order = #{sortOrder},
            is_system = #{isSystem},
            status = #{status},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除权限 -->
    <delete id="deleteById">
        DELETE FROM permissions WHERE id = #{id}
    </delete>

    <!-- 批量删除权限 -->
    <delete id="deleteByIds">
        DELETE FROM permissions WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
