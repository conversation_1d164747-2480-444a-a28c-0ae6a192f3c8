package zh.backend.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import zh.backend.entity.Role;

import java.util.List;

/**
 * 角色更新请求DTO
 */
public class RoleUpdateRequest {

    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    private String name;

    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    private Role.RoleStatus status;

    @Min(value = 0, message = "排序值不能小于0")
    @Max(value = 999, message = "排序值不能大于999")
    private Integer sortOrder;

    private List<Long> permissionIds;

    // 构造函数
    public RoleUpdateRequest() {}

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Role.RoleStatus getStatus() {
        return status;
    }

    public void setStatus(Role.RoleStatus status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<Long> getPermissionIds() {
        return permissionIds;
    }

    public void setPermissionIds(List<Long> permissionIds) {
        this.permissionIds = permissionIds;
    }

    // 更新实体
    public void updateEntity(Role role) {
        role.setName(this.name);
        role.setDescription(this.description);
        if (this.status != null) {
            role.setStatus(this.status);
        }
        if (this.sortOrder != null) {
            role.setSortOrder(this.sortOrder);
        }
    }
}
