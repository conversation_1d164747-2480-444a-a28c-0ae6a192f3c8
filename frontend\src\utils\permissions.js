/**
 * 权限管理工具类
 * 统一管理前端的权限检查逻辑
 */

// 权限常量定义 - 适配新的后端权限编码格式
export const PERMISSIONS = {
  // 用户管理权限模块
  USER_MANAGEMENT: 'USER_MANAGEMENT',
  USER_LIST: 'USER_LIST',
  USER_CREATE: 'USER_CREATE',
  USER_UPDATE: 'USER_UPDATE',
  USER_DELETE: 'USER_DELETE',
  USER_RESET_PASSWORD: 'USER_RESET_PASSWORD',
  USER_STATUS_MANAGE: 'USER_STATUS_MANAGE',
  USER_ROLE_ASSIGN: 'USER_ROLE_ASSIGN',

  // 角色管理权限模块
  ROLE_MANAGEMENT: 'ROLE_MANAGEMENT',
  ROLE_LIST: 'ROLE_LIST',
  ROLE_CREATE: 'ROLE_CREATE',
  ROLE_UPDATE: 'ROLE_UPDATE',
  ROLE_DELETE: 'ROLE_DELETE',
  ROLE_ASSIGN_PERMISSIONS: 'ROLE_ASSIGN_PERMISSIONS',
  ROLE_USER_MANAGE: 'ROLE_USER_MANAGE',

  // 权限管理权限模块
  PERMISSION_MANAGEMENT: 'PERMISSION_MANAGEMENT',
  PERMISSION_LIST: 'PERMISSION_LIST',
  PERMISSION_CREATE: 'PERMISSION_CREATE',
  PERMISSION_UPDATE: 'PERMISSION_UPDATE',
  PERMISSION_DELETE: 'PERMISSION_DELETE',
  PERMISSION_TREE: 'PERMISSION_TREE',

  // 系统监控权限模块
  SYSTEM_MONITOR: 'SYSTEM_MONITOR',
  LOG_VIEW: 'LOG_VIEW',
  ONLINE_USERS: 'ONLINE_USERS',
  HEALTH_CHECK: 'HEALTH_CHECK',

  // 个人中心权限模块
  PROFILE: 'PROFILE',
  PROFILE_VIEW: 'PROFILE_VIEW',
  PROFILE_UPDATE: 'PROFILE_UPDATE',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',

  // 仪表盘权限模块
  DASHBOARD: 'DASHBOARD',
  DASHBOARD_STATS: 'DASHBOARD_STATS',
  DASHBOARD_SYSTEM_STATUS: 'DASHBOARD_SYSTEM_STATUS',

  // 认证权限模块
  AUTH_MANAGEMENT: 'AUTH_MANAGEMENT',
  AUTH_LOGIN: 'AUTH_LOGIN',
  AUTH_REGISTER: 'AUTH_REGISTER',
  AUTH_LOGOUT: 'AUTH_LOGOUT',
  AUTH_ME: 'AUTH_ME',


}

// 角色常量定义 - 适配新的后端角色编码
export const ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
  USER: 'USER'
}

// 权限分组定义
export const PERMISSION_GROUPS = {
  USER_MANAGEMENT: [
    PERMISSIONS.USER_MANAGEMENT,
    PERMISSIONS.USER_LIST,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_RESET_PASSWORD,
    PERMISSIONS.USER_STATUS_MANAGE,
    PERMISSIONS.USER_ROLE_ASSIGN
  ],
  ROLE_MANAGEMENT: [
    PERMISSIONS.ROLE_MANAGEMENT,
    PERMISSIONS.ROLE_LIST,
    PERMISSIONS.ROLE_CREATE,
    PERMISSIONS.ROLE_UPDATE,
    PERMISSIONS.ROLE_DELETE,
    PERMISSIONS.ROLE_ASSIGN_PERMISSIONS,
    PERMISSIONS.ROLE_USER_MANAGE
  ],
  PERMISSION_MANAGEMENT: [
    PERMISSIONS.PERMISSION_MANAGEMENT,
    PERMISSIONS.PERMISSION_LIST,
    PERMISSIONS.PERMISSION_CREATE,
    PERMISSIONS.PERMISSION_UPDATE,
    PERMISSIONS.PERMISSION_DELETE,
    PERMISSIONS.PERMISSION_TREE
  ],
  SYSTEM_MANAGEMENT: [
    PERMISSIONS.SYSTEM_MONITOR,
    PERMISSIONS.LOG_VIEW,
    PERMISSIONS.ONLINE_USERS,
    PERMISSIONS.HEALTH_CHECK
  ],
  DASHBOARD_MANAGEMENT: [
    PERMISSIONS.DASHBOARD,
    PERMISSIONS.DASHBOARD_STATS,
    PERMISSIONS.DASHBOARD_SYSTEM_STATUS
  ],
  PROFILE_MANAGEMENT: [
    PERMISSIONS.PROFILE,
    PERMISSIONS.PROFILE_VIEW,
    PERMISSIONS.PROFILE_UPDATE,
    PERMISSIONS.PASSWORD_CHANGE
  ],
  AUTH_MANAGEMENT: [
    PERMISSIONS.AUTH_MANAGEMENT,
    PERMISSIONS.AUTH_LOGIN,
    PERMISSIONS.AUTH_REGISTER,
    PERMISSIONS.AUTH_LOGOUT,
    PERMISSIONS.AUTH_ME
  ]
}

/**
 * 检查用户是否拥有指定权限
 * @param {Object} user - 用户对象
 * @param {string|Array} permissions - 权限名称或权限数组
 * @param {string} mode - 检查模式：'any'(任一权限) 或 'all'(所有权限)
 * @returns {boolean}
 */
export function hasPermission(user, permissions, mode = 'any') {
  if (!user) return false

  const userPermissions = user.permissions || []
  const userRoles = user.roles || []

  // 超级管理员拥有所有权限
  if (userRoles.includes(ROLES.SUPER_ADMIN)) {
    return true
  }

  // 标准化权限参数为数组
  const permissionList = Array.isArray(permissions) ? permissions : [permissions]

  if (mode === 'all') {
    // 需要拥有所有指定权限
    return permissionList.every(permission => userPermissions.includes(permission))
  } else {
    // 只需要拥有任一权限
    return permissionList.some(permission => userPermissions.includes(permission))
  }
}

/**
 * 检查用户是否拥有指定角色
 * @param {Object} user - 用户对象
 * @param {string|Array} roles - 角色名称或角色数组
 * @returns {boolean}
 */
export function hasRole(user, roles) {
  if (!user) return false

  const userRoles = user.roles || []

  // 超级管理员拥有所有角色
  if (userRoles.includes(ROLES.SUPER_ADMIN)) {
    return true
  }

  const roleList = Array.isArray(roles) ? roles : [roles]

  return roleList.some(role => userRoles.includes(role))
}

/**
 * 检查用户是否可以访问指定路由
 * @param {Object} user - 用户对象
 * @param {Object} route - 路由对象
 * @returns {boolean}
 */
export function canAccessRoute(user, route) {
  if (!route.meta) return true

  // 检查角色要求
  if (route.meta.requiresAdmin) {
    return hasRole(user, [ROLES.SUPER_ADMIN, ROLES.SYSTEM_ADMIN])
  }

  // 检查权限要求
  if (route.meta.requiresPermission) {
    return hasPermission(user, route.meta.requiresPermission)
  }

  return true
}

/**
 * 检查用户是否可以执行指定操作
 * @param {Object} user - 用户对象
 * @param {string} resource - 资源类型
 * @param {string} action - 操作类型
 * @returns {boolean}
 */
export function canPerformAction(user, resource, action) {
  const permission = `${resource}:${action}`
  return hasPermission(user, permission)
}

/**
 * 获取用户可访问的管理菜单
 * @param {Object} user - 用户对象
 * @returns {Array} 菜单数组
 */
export function getAccessibleAdminMenus(user) {
  if (!user) return []

  const menus = []

  // 用户管理菜单
  if (hasPermission(user, PERMISSIONS.USER_LIST) || hasRole(user, [ROLES.SUPER_ADMIN, ROLES.SYSTEM_ADMIN])) {
    menus.push({
      id: 'users',
      name: '用户管理',
      route: '/admin/users',
      permission: PERMISSIONS.USER_LIST
    })
  }

  // 角色管理菜单
  if (hasPermission(user, PERMISSIONS.ROLE_LIST) || hasRole(user, ROLES.SUPER_ADMIN)) {
    menus.push({
      id: 'roles',
      name: '角色管理',
      route: '/admin/roles',
      permission: PERMISSIONS.ROLE_LIST
    })
  }

  // 权限管理菜单
  if (hasPermission(user, PERMISSIONS.PERMISSION_LIST) || hasRole(user, ROLES.SUPER_ADMIN)) {
    menus.push({
      id: 'permissions',
      name: '权限管理',
      route: '/admin/permissions',
      permission: PERMISSIONS.PERMISSION_LIST
    })
  }



  return menus
}

/**
 * 获取权限的中文描述
 * @param {string} permission - 权限名称
 * @returns {string} 中文描述
 */
export function getPermissionDescription(permission) {
  const descriptions = {
    // 用户管理权限
    [PERMISSIONS.USER_MANAGEMENT]: '用户管理模块',
    [PERMISSIONS.USER_LIST]: '查看用户列表',
    [PERMISSIONS.USER_CREATE]: '创建用户',
    [PERMISSIONS.USER_UPDATE]: '更新用户信息',
    [PERMISSIONS.USER_DELETE]: '删除用户',
    [PERMISSIONS.USER_RESET_PASSWORD]: '重置用户密码',
    [PERMISSIONS.USER_STATUS_MANAGE]: '管理用户状态',
    [PERMISSIONS.USER_ROLE_ASSIGN]: '分配用户角色',

    // 角色管理权限
    [PERMISSIONS.ROLE_MANAGEMENT]: '角色管理模块',
    [PERMISSIONS.ROLE_LIST]: '查看角色列表',
    [PERMISSIONS.ROLE_CREATE]: '创建角色',
    [PERMISSIONS.ROLE_UPDATE]: '更新角色信息',
    [PERMISSIONS.ROLE_DELETE]: '删除角色',
    [PERMISSIONS.ROLE_ASSIGN_PERMISSIONS]: '分配角色权限',
    [PERMISSIONS.ROLE_USER_MANAGE]: '管理角色用户',

    // 权限管理权限
    [PERMISSIONS.PERMISSION_MANAGEMENT]: '权限管理模块',
    [PERMISSIONS.PERMISSION_LIST]: '查看权限列表',
    [PERMISSIONS.PERMISSION_CREATE]: '创建权限',
    [PERMISSIONS.PERMISSION_UPDATE]: '更新权限信息',
    [PERMISSIONS.PERMISSION_DELETE]: '删除权限',
    [PERMISSIONS.PERMISSION_TREE]: '查看权限树',

    // 系统监控权限
    [PERMISSIONS.SYSTEM_MONITOR]: '系统监控模块',
    [PERMISSIONS.LOG_VIEW]: '查看系统日志',
    [PERMISSIONS.ONLINE_USERS]: '在线用户管理',
    [PERMISSIONS.HEALTH_CHECK]: '系统健康检查',

    // 个人中心权限
    [PERMISSIONS.PROFILE]: '个人中心模块',
    [PERMISSIONS.PROFILE_VIEW]: '查看个人资料',
    [PERMISSIONS.PROFILE_UPDATE]: '更新个人资料',
    [PERMISSIONS.PASSWORD_CHANGE]: '修改个人密码',

    // 仪表盘权限
    [PERMISSIONS.DASHBOARD]: '仪表盘模块',
    [PERMISSIONS.DASHBOARD_STATS]: '查看统计数据',
    [PERMISSIONS.DASHBOARD_SYSTEM_STATUS]: '查看系统状态',

    // 认证权限
    [PERMISSIONS.AUTH_MANAGEMENT]: '认证管理模块',
    [PERMISSIONS.AUTH_LOGIN]: '用户登录',
    [PERMISSIONS.AUTH_REGISTER]: '用户注册',
    [PERMISSIONS.AUTH_LOGOUT]: '用户登出',
    [PERMISSIONS.AUTH_ME]: '获取用户信息'
  }

  return descriptions[permission] || permission
}

export default {
  PERMISSIONS,
  ROLES,
  PERMISSION_GROUPS,
  hasPermission,
  hasRole,
  canAccessRoute,
  canPerformAction,
  getAccessibleAdminMenus,
  getPermissionDescription
}