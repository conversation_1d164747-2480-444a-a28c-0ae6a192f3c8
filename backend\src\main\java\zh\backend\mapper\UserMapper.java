package zh.backend.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zh.backend.entity.User;

import java.util.List;

/**
 * 用户 MyBatis Mapper
 */
@Mapper
public interface UserMapper {

    /**
     * 根据ID查找用户
     */
    User findById(@Param("id") Long id);

    /**
     * 根据用户名查找用户
     */
    User findByUsername(@Param("username") String username);

    /**
     * 根据用户名查找用户（包含角色和权限）
     */
    User findByUsernameWithRolesAndPermissions(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     */
    User findByEmail(@Param("email") String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 查找所有用户
     */
    List<User> findAll();

    /**
     * 根据状态查找用户
     */
    List<User> findByStatus(@Param("status") String status);

    /**
     * 分页查询用户（支持关键字搜索）
     */
    List<User> findByKeyword(
        @Param("keyword") String keyword,
        @Param("status") String status,
        @Param("roleId") Long roleId,
        @Param("offset") int offset,
        @Param("size") int size,
        @Param("sortBy") String sortBy,
        @Param("sortDir") String sortDir
    );

    /**
     * 统计关键字搜索结果数量
     */
    long countByKeyword(
        @Param("keyword") String keyword,
        @Param("status") String status,
        @Param("roleId") Long roleId
    );

    /**
     * 根据角色查找用户
     */
    List<User> findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据权限查找用户
     */
    List<User> findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 统计用户数量
     */
    long count();

    /**
     * 根据状态统计用户数量
     */
    long countByStatus(@Param("status") String status);

    /**
     * 根据角色统计用户数量
     */
    List<Object[]> countByRole();

    /**
     * 插入用户
     */
    int insertUser(User user);

    /**
     * 更新用户
     */
    int updateUser(User user);

    /**
     * 根据ID删除用户
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除用户
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 更新用户最后登录时间
     */
    int updateLastLoginTime(@Param("id") Long id);

    /**
     * 更新用户状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新用户密码
     */
    int updatePassword(@Param("id") Long id, @Param("password") String password);
}
