package zh.backend.dto;

import java.util.List;

/**
 * 角色模板DTO
 */
public class RoleTemplateDTO {
    private String code;
    private String name;
    private String description;
    private String category;
    private List<String> permissionCodes;
    private Integer sortOrder;
    private boolean isBuiltIn;

    // 构造函数
    public RoleTemplateDTO() {}

    public RoleTemplateDTO(String code, String name, String description, String category, 
                          List<String> permissionCodes, Integer sortOrder, boolean isBuiltIn) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.category = category;
        this.permissionCodes = permissionCodes;
        this.sortOrder = sortOrder;
        this.isBuiltIn = isBuiltIn;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getPermissionCodes() {
        return permissionCodes;
    }

    public void setPermissionCodes(List<String> permissionCodes) {
        this.permissionCodes = permissionCodes;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public boolean isBuiltIn() {
        return isBuiltIn;
    }

    public void setBuiltIn(boolean builtIn) {
        isBuiltIn = builtIn;
    }
}
