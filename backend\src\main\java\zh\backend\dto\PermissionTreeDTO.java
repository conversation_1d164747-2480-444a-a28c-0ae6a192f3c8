package zh.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import zh.backend.entity.Permission;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限树数据传输对象
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PermissionTreeDTO {
    
    private Long id;
    private String name;
    private String code;
    private String description;
    private Permission.ResourceType resourceType;
    private String resourcePath;
    private Permission.HttpMethod httpMethod;
    private Long parentId;
    private Integer level;
    private Integer sortOrder;
    private Permission.PermissionStatus status;
    private Boolean isSystem;
    private List<PermissionTreeDTO> children;
    
    // 构造函数
    public PermissionTreeDTO() {
        this.children = new ArrayList<>();
    }
    
    public PermissionTreeDTO(Permission permission) {
        this();
        this.id = permission.getId();
        this.name = permission.getName();
        this.code = permission.getCode();
        this.description = permission.getDescription();
        this.resourceType = permission.getResourceType();
        this.resourcePath = permission.getResourcePath();
        this.httpMethod = permission.getHttpMethod();
        this.parentId = permission.getParentId();
        this.level = permission.getLevel();
        this.sortOrder = permission.getSortOrder();
        this.status = permission.getStatus();
        this.isSystem = permission.getIsSystem();
    }
    
    // 添加子权限
    public void addChild(PermissionTreeDTO child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }
    
    // 检查是否有子权限
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Permission.ResourceType getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(Permission.ResourceType resourceType) {
        this.resourceType = resourceType;
    }
    
    public String getResourcePath() {
        return resourcePath;
    }
    
    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }
    
    public Permission.HttpMethod getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(Permission.HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Permission.PermissionStatus getStatus() {
        return status;
    }
    
    public void setStatus(Permission.PermissionStatus status) {
        this.status = status;
    }
    
    public Boolean getIsSystem() {
        return isSystem;
    }
    
    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
    
    public List<PermissionTreeDTO> getChildren() {
        return children;
    }
    
    public void setChildren(List<PermissionTreeDTO> children) {
        this.children = children;
    }
}
