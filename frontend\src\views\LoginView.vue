<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <div class="logo">
          <h1>🔐 系统登录</h1>
          <p>欢迎回来，请登录您的账户</p>
        </div>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="usernameOrEmail">
            <i class="icon">👤</i>
            用户名或邮箱
          </label>
          <input
            type="text"
            id="usernameOrEmail"
            v-model="usernameOrEmail"
            :class="{ 'error': errors.usernameOrEmail }"
            placeholder="请输入用户名或邮箱"
            required
            @blur="validateUsernameOrEmail"
            @input="clearError('usernameOrEmail')"
            @keydown.enter="focusPassword"
            autocomplete="username"
          />
          <span v-if="errors.usernameOrEmail" class="field-error">{{ errors.usernameOrEmail }}</span>
        </div>

        <div class="form-group">
          <label for="password">
            <i class="icon">🔒</i>
            密码
          </label>
          <div class="password-input">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="password"
              :class="{ 'error': errors.password }"
              placeholder="请输入密码"
              required
              @blur="validatePassword"
              @input="clearError('password')"
              @keydown.enter="handleLogin"
              autocomplete="current-password"
              ref="passwordInput"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showPassword = !showPassword"
              :title="showPassword ? '隐藏密码' : '显示密码'"
            >
              <i class="toggle-icon" :class="{ 'visible': showPassword }">
                {{ showPassword ? '🙈' : '👁️' }}
              </i>
            </button>
          </div>
          <span v-if="errors.password" class="field-error">{{ errors.password }}</span>
        </div>

        <div class="form-options">
          <label class="remember-me">
            <input type="checkbox" v-model="rememberMe">
            <span class="checkmark"></span>
            记住我
          </label>
          <a href="#" class="forgot-password" @click.prevent="showForgotPassword">忘记密码？</a>
        </div>

        <button
          type="submit"
          class="login-button"
          :disabled="authStore.loading || !isFormValid"
          :class="{ 'loading': authStore.loading }"
        >
          <span v-if="authStore.loading" class="spinner"></span>
          {{ authStore.loading ? '登录中...' : '登录' }}
        </button>

        <div v-if="authStore.error" class="error-message">
          <i class="icon">⚠️</i>
          {{ authStore.error }}
          <button type="button" class="error-close" @click="clearAuthError" title="关闭">×</button>
        </div>

        <div v-if="loginSuccess" class="success-message">
          <i class="icon">🎉</i>
          <div class="success-content">
            <h4>登录成功！</h4>
            <p>正在跳转到首页...</p>
            <div class="success-progress">
              <div class="progress-bar"></div>
            </div>
          </div>
        </div>
      </form>

      <div class="login-footer">
        <p>还没有账户？
          <router-link to="/register" class="register-link">立即注册</router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const usernameOrEmail = ref('');
const password = ref('');
const showPassword = ref(false);
const rememberMe = ref(false);
const loginSuccess = ref(false);
const errors = ref({});
const passwordInput = ref(null);

const authStore = useAuthStore();
const router = useRouter();

// 焦点控制
const focusPassword = () => {
  if (passwordInput.value) {
    passwordInput.value.focus();
  }
};

// 表单验证
const validateUsernameOrEmail = () => {
  if (!usernameOrEmail.value.trim()) {
    errors.value.usernameOrEmail = '请输入用户名或邮箱';
    return false;
  }
  
  // 如果输入包含@符号，验证邮箱格式
  if (usernameOrEmail.value.includes('@')) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(usernameOrEmail.value)) {
      errors.value.usernameOrEmail = '请输入有效的邮箱地址';
      return false;
    }
  } else {
    // 验证用户名格式
    if (usernameOrEmail.value.length < 3) {
      errors.value.usernameOrEmail = '用户名长度至少3位';
      return false;
    }
    if (!/^[a-zA-Z0-9_]+$/.test(usernameOrEmail.value)) {
      errors.value.usernameOrEmail = '用户名只能包含字母、数字和下划线';
      return false;
    }
  }
  
  return true;
};

const validatePassword = () => {
  if (!password.value.trim()) {
    errors.value.password = '请输入密码';
    return false;
  }
  if (password.value.length < 6) {
    errors.value.password = '密码长度至少6位';
    return false;
  }
  return true;
};

const clearError = (field) => {
  if (errors.value[field]) {
    delete errors.value[field];
  }
};

const clearAuthError = () => {
  authStore.error = null;
};

const isFormValid = computed(() => {
  return usernameOrEmail.value.trim() &&
         password.value.trim() &&
         Object.keys(errors.value).length === 0;
});

// 登录处理
const handleLogin = async () => {
  if (!validateUsernameOrEmail() || !validatePassword()) {
    return;
  }

  try {
    await authStore.login({
      usernameOrEmail: usernameOrEmail.value,
      password: password.value
    });
    
    if (authStore.isAuthenticated) {
      loginSuccess.value = true;
      
      // 记住我功能
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true');
        localStorage.setItem('lastUsername', usernameOrEmail.value);
      } else {
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('lastUsername');
      }
      
      setTimeout(() => {
        router.push('/');
      }, 1000);
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
};

const showForgotPassword = () => {
  alert('忘记密码功能暂未实现，请联系管理员重置密码。');
};

// 记住我功能和自动聚焦
onMounted(() => {
  if (localStorage.getItem('rememberMe') === 'true') {
    rememberMe.value = true;
    usernameOrEmail.value = localStorage.getItem('lastUsername') || '';
    // 如果记住了用户名，自动聚焦到密码框
    if (usernameOrEmail.value && passwordInput.value) {
      setTimeout(() => {
        passwordInput.value.focus();
      }, 100);
    }
  } else {
    // 否则聚焦到用户名框
    const usernameInput = document.getElementById('usernameOrEmail');
    if (usernameInput) {
      setTimeout(() => {
        usernameInput.focus();
      }, 100);
    }
  }
});
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

button {
  width: 100%;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:disabled {
  background-color: #aaa;
}

.error-message {
  color: red;
  margin-top: 10px;
  text-align: center;
}

p {
  text-align: center;
  margin-top: 20px;
}
</style>
<style scoped>
.login-page {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 420px;
  border: 1px solid #e5e7eb;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo h1 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
}

.logo p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

label {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.icon {
  margin-right: 8px;
  font-size: 16px;
}

input[type="text"],
input[type="password"],
input[type="email"] {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: #f8f9fa;
}

input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.field-error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #666;
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.password-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #4f46e5;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.toggle-icon.visible {
  transform: scale(1.1);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-me {
  display: flex !important;
  align-items: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  margin: 0 !important;
}

.remember-me input {
  width: auto !important;
  margin-right: 8px;
  margin-bottom: 0 !important;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff3d;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message, .success-message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
}

.error-message {
  background: #fff5f5;
  color: #dc3545;
  border: 1px solid #f5c6cb;
  padding-right: 40px;
}

.error-close {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.error-close:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.success-message {
  background: #f0fff4;
  color: #28a745;
  border: 1px solid #c3e6cb;
  align-items: flex-start;
  padding: 16px;
}

.success-content {
  flex: 1;
  margin-left: 8px;
}

.success-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.success-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  opacity: 0.8;
}

.success-progress {
  width: 100%;
  height: 4px;
  background: rgba(40, 167, 69, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #28a745;
  width: 0%;
  animation: progressLoad 1s ease-out forwards;
}

@keyframes progressLoad {
  to {
    width: 100%;
  }
}

.login-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e1e5e9;
}

.login-footer p {
  color: #666;
  margin: 0 0 24px 0;
  font-size: 14px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link:hover {
  text-decoration: underline;
}


/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    padding: 16px;
  }
  
  .login-container {
    padding: 32px;
    max-width: 90%;
  }
  
  .logo h1 {
    font-size: 26px;
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: 12px;
    align-items: flex-start;
    padding-top: 20px;
  }
  
  .login-container {
    padding: 24px;
    margin: 0;
    max-width: 100%;
    border-radius: 16px;
  }
  
  .logo h1 {
    font-size: 24px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  input[type="text"],
  input[type="password"],
  input[type="email"] {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 12px 14px;
  }
  
  .login-button {
    padding: 14px;
    font-size: 15px;
  }
}

/* 动画效果 */
.login-container {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>