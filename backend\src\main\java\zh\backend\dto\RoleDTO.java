package zh.backend.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import zh.backend.entity.Role;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色数据传输对象
 */
public class RoleDTO {
    private Long id;

    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    private String name;

    @NotBlank(message = "角色编码不能为空")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "角色编码必须以大写字母开头，只能包含大写字母、数字和下划线")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    private String code;

    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    private Role.RoleStatus status = Role.RoleStatus.ACTIVE;
    
    private Boolean isSystem = false;
    
    private Integer sortOrder = 0;
    
    private Long permissionCount = 0L;
    
    private Long userCount = 0L;
    
    private List<Long> permissionIds;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;

    // 构造函数
    public RoleDTO() {}

    public RoleDTO(Role role) {
        this.id = role.getId();
        this.name = role.getName();
        this.code = role.getCode();
        this.description = role.getDescription();
        this.status = role.getStatus();
        this.isSystem = role.getIsSystem();
        this.sortOrder = role.getSortOrder();
        this.permissionCount = (long) role.getPermissions().size();
        this.userCount = (long) role.getUsers().size();
        this.createdAt = role.getCreatedAt();
        this.updatedAt = role.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Role.RoleStatus getStatus() {
        return status;
    }

    public void setStatus(Role.RoleStatus status) {
        this.status = status;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean system) {
        isSystem = system;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getPermissionCount() {
        return permissionCount;
    }

    public void setPermissionCount(Long permissionCount) {
        this.permissionCount = permissionCount;
    }

    public Long getUserCount() {
        return userCount;
    }

    public void setUserCount(Long userCount) {
        this.userCount = userCount;
    }

    public List<Long> getPermissionIds() {
        return permissionIds;
    }

    public void setPermissionIds(List<Long> permissionIds) {
        this.permissionIds = permissionIds;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 转换为实体
    public Role toEntity() {
        Role role = new Role();
        role.setId(this.id);
        role.setName(this.name);
        role.setCode(this.code);
        role.setDescription(this.description);
        role.setStatus(this.status);
        role.setIsSystem(this.isSystem);
        role.setSortOrder(this.sortOrder);
        return role;
    }

    // 更新实体
    public void updateEntity(Role role) {
        role.setName(this.name);
        role.setDescription(this.description);
        role.setStatus(this.status);
        role.setSortOrder(this.sortOrder);
    }
}
