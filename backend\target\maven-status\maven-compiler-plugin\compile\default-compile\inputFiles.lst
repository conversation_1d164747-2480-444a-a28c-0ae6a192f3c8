D:\Code\bbbbbb\backend\src\main\java\zh\backend\annotation\RequireRole.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RoleUpdateRequest.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\BaseEntity.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\impl\RoleServiceImpl.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\RoleController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\UserPermission.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RoleCreateRequest.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\PermissionDTO.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\UserService.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\filter\JwtAuthenticationFilter.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\User.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\config\CacheConfig.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\Permission.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\PermissionService.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RolePermissionAssignRequest.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\PermissionController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\mapper\PermissionMapper.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RoleDTO.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\UserPermissionDTO.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\Role.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\UserController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\RoleService.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\PermissionPolicy.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\HealthController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\PermissionTreeDTO.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\mapper\UserPermissionMapper.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\mapper\UserMapper.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\aspect\PermissionAspect.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\config\DataInitializer.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\BackendApplication.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\config\SecurityConfig.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\ApiResponse.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\mapper\RoleMapper.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\ProfileController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\util\JwtUtil.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RoleTemplateDTO.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\TestController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\impl\PermissionServiceImpl.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\controller\AuthController.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\entity\PermissionLog.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\service\impl\UserServiceImpl.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\annotation\RequirePermission.java
D:\Code\bbbbbb\backend\src\main\java\zh\backend\dto\RoleStatisticsDTO.java
