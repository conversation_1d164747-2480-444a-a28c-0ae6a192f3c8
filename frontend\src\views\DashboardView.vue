<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-wrapper">
            <div class="title-icon-wrapper">
              <el-icon class="title-icon"><Monitor /></el-icon>
            </div>
            <div class="title-text">
              <h1 class="page-title">仪表盘</h1>
              <p class="page-subtitle">数据总览与快速操作中心</p>
            </div>
          </div>
          <div class="welcome-message">
            <div class="greeting">
              <span class="greeting-text">{{ getGreeting() }}，</span>
              <span class="user-display-name">{{ user?.realName || user?.username }}</span>
              <el-icon class="greeting-icon"><Star /></el-icon>
            </div>
            <p class="current-time">{{ currentDateTime }}</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="user-card">
            <el-avatar :icon="UserFilled" :size="48" class="user-avatar">
              <img v-if="user?.avatar" :src="user.avatar" alt="用户头像" />
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ user?.realName || user?.username }}</div>
              <div class="user-role-badge">
                <el-tag
                  v-for="role in user?.roles?.slice(0, 2)"
                  :key="role"
                  :type="getRoleTagType(role)"
                  effect="light"
                  size="small"
                  class="role-tag"
                >
                  {{ formatRoles([role]) }}
                </el-tag>
                <el-tag v-if="(user?.roles?.length || 0) > 2" type="info" effect="plain" size="small">
                  +{{ (user?.roles?.length || 0) - 2 }}
                </el-tag>
              </div>
            </div>
            <div class="online-status">
              <div class="status-dot"></div>
              <span class="status-text">在线</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card stats-users" shadow="hover" @click="$router.push('/admin/users')" v-loading="loading.stats">
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <div class="stats-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stats-bg-pattern"></div>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ animatedStats.totalUsers }}</div>
                <div class="stats-label">总用户数</div>
                <div class="stats-trend">
                  <el-icon class="trend-icon up"><TrendCharts /></el-icon>
                  <span class="trend-text">{{ stats.userTrend }}</span>
                </div>
              </div>
            </div>
            <div class="stats-footer">
              <span class="footer-text">点击查看详情</span>
              <el-icon class="footer-arrow"><ArrowRight /></el-icon>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card stats-online" shadow="hover" v-loading="loading.stats">
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <div class="stats-icon">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stats-bg-pattern"></div>
                <div class="pulse-dot"></div>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ animatedStats.onlineUsers }}</div>
                <div class="stats-label">在线用户</div>
                <div class="stats-trend">
                  <div class="real-time-indicator">
                    <div class="live-dot"></div>
                    <span class="live-text">实时数据</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="stats-footer">
              <span class="footer-text">活跃用户监控</span>
              <el-icon class="footer-icon"><View /></el-icon>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card stats-roles" shadow="hover" @click="$router.push('/admin/roles')" v-loading="loading.stats">
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <div class="stats-icon">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="stats-bg-pattern"></div>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ animatedStats.totalRoles }}</div>
                <div class="stats-label">系统角色</div>
                <div class="stats-trend">
                  <el-icon class="trend-icon stable"><Minus /></el-icon>
                  <span class="trend-text">配置稳定</span>
                </div>
              </div>
            </div>
            <div class="stats-footer">
              <span class="footer-text">角色权限管理</span>
              <el-icon class="footer-arrow"><ArrowRight /></el-icon>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card stats-permissions" shadow="hover" @click="$router.push('/admin/permissions')" v-loading="loading.stats">
            <div class="stats-content">
              <div class="stats-icon-wrapper">
                <div class="stats-icon">
                  <el-icon><Key /></el-icon>
                </div>
                <div class="stats-bg-pattern"></div>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ animatedStats.totalPermissions }}</div>
                <div class="stats-label">系统权限</div>
                <div class="stats-trend">
                  <el-icon class="trend-icon up"><Plus /></el-icon>
                  <span class="trend-text">{{ stats.permissionTrend }}</span>
                </div>
              </div>
            </div>
            <div class="stats-footer">
              <span class="footer-text">权限配置中心</span>
              <el-icon class="footer-arrow"><ArrowRight /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 个人信息 -->
        <el-col :xs="24" :lg="8">
          <el-card class="content-card profile-card" shadow="never">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <el-icon class="header-icon"><User /></el-icon>
                  <h4 class="section-title">个人资料</h4>
                </div>
                <el-button type="primary" link size="small" @click="$router.push('/profile')" class="edit-btn">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
              </div>
            </template>
            <div class="profile-content">
              <div class="profile-avatar-section">
                <el-avatar :size="60" :icon="UserFilled" class="profile-avatar">
                  <img v-if="user?.avatar" :src="user.avatar" alt="头像" />
                </el-avatar>
                <div class="avatar-info">
                  <div class="display-name">{{ user?.realName || user?.username }}</div>
                  <div class="username">@{{ user?.username }}</div>
                </div>
              </div>
              
              <div class="profile-details">
                <div class="detail-item">
                  <el-icon class="detail-icon"><Message /></el-icon>
                  <span class="detail-label">邮箱</span>
                  <span class="detail-value">{{ user?.email || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <el-icon class="detail-icon"><Calendar /></el-icon>
                  <span class="detail-label">加入时间</span>
                  <span class="detail-value">{{ formatJoinDate() }}</span>
                </div>
                <div class="detail-item">
                  <el-icon class="detail-icon"><UserFilled /></el-icon>
                  <span class="detail-label">用户角色</span>
                  <div class="roles-container">
                    <el-tag
                      v-for="role in user?.roles"
                      :key="role"
                      :type="getRoleTagType(role)"
                      effect="light"
                      size="small"
                      class="role-tag"
                    >
                      {{ formatRoles([role]) }}
                    </el-tag>
                    <span v-if="!user?.roles || user.roles.length === 0" class="no-role">普通用户</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 我的权限 -->
        <el-col :xs="24" :lg="8">
          <el-card class="content-card permissions-card" shadow="never">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <el-icon class="header-icon"><Key /></el-icon>
                  <h4 class="section-title">我的权限</h4>
                </div>
                <el-badge :value="user?.permissions?.length || 0" type="primary" class="permission-count">
                  <el-icon><Setting /></el-icon>
                </el-badge>
              </div>
            </template>
            <div class="permissions-section">
              <div v-if="user?.permissions && user.permissions.length > 0" class="permissions-list">
                <div
                  v-for="permission in displayedPermissions"
                  :key="permission.name"
                  class="permission-item"
                  :title="permission.description"
                >
                  <div class="permission-icon">
                    <el-icon><Key /></el-icon>
                  </div>
                  <div class="permission-content">
                    <div class="permission-name">{{ permission.description || permission.name }}</div>
                    <div class="permission-code">{{ permission.name }}</div>
                  </div>
                  <div class="permission-badge">
                    <el-tag size="small" :type="getPermissionType(permission.resource)" effect="plain">
                      {{ permission.resource }}
                    </el-tag>
                  </div>
                </div>
                <div v-if="(user?.permissions?.length || 0) > 5" class="show-more">
                  <el-button text type="primary" size="small" @click="showAllPermissions = !showAllPermissions">
                    {{ showAllPermissions ? '收起' : `查看全部 ${user.permissions.length} 项权限` }}
                    <el-icon><component :is="showAllPermissions ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  </el-button>
                </div>
              </div>
              <div v-else class="no-permissions">
                <el-empty description="您当前没有特殊权限" :image-size="80">
                  <template #description>
                    <p>您拥有基本用户操作权限</p>
                    <p class="empty-tip">如需更多权限，请联系管理员</p>
                  </template>
                </el-empty>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 系统状态 -->
        <el-col :xs="24" :lg="8">
          <el-card class="content-card system-card" shadow="never" v-loading="loading.systemStatus">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <el-icon class="header-icon"><Monitor /></el-icon>
                  <h4 class="section-title">系统状态</h4>
                </div>
                <div class="system-status">
                  <div class="status-dot running"></div>
                  <span class="status-text">{{ systemStatus.status }}</span>
                </div>
              </div>
            </template>
            <div class="system-content">
              <div class="system-metrics">
                <div class="metric-item">
                  <div class="metric-icon">
                    <el-icon><Cpu /></el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">CPU使用率</div>
                    <div class="metric-value">{{ systemStatus.cpu }}%</div>
                    <el-progress :percentage="systemStatus.cpu" :show-text="false" :stroke-width="4" />
                  </div>
                </div>
                
                <div class="metric-item">
                  <div class="metric-icon">
                    <el-icon><Memo /></el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">内存使用率</div>
                    <div class="metric-value">{{ systemStatus.memory }}%</div>
                    <el-progress :percentage="systemStatus.memory" :show-text="false" :stroke-width="4" color="#f56c6c" />
                  </div>
                </div>
                
                <div class="metric-item">
                  <div class="metric-icon">
                    <el-icon><Connection /></el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">网络状态</div>
                    <div class="metric-value">{{ systemStatus.network }}</div>
                    <div class="network-indicator">
                      <div class="signal-bar" :class="{ active: true }"></div>
                      <div class="signal-bar" :class="{ active: true }"></div>
                      <div class="signal-bar" :class="{ active: true }"></div>
                      <div class="signal-bar" :class="{ active: systemStatus.network === '优秀' }"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="system-info">
                <div class="info-row">
                  <span class="info-label">服务器时间</span>
                  <span class="info-value">{{ systemStatus.currentTime || currentDateTime }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">运行时长</span>
                  <span class="info-value">{{ systemStatus.uptime }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <el-card class="content-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4 class="section-title">快速操作</h4>
        </div>
      </template>
      <div class="quick-actions">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="8">
            <div class="action-item" @click="refreshAllData">
              <el-icon class="action-icon"><Refresh /></el-icon>
              <span class="action-text">刷新数据</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="action-item" @click="$router.push('/help')">
              <el-icon class="action-icon"><QuestionFilled /></el-icon>
              <span class="action-text">帮助中心</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="action-item logout-action" @click="handleLogout">
              <el-icon class="action-icon"><SwitchButton /></el-icon>
              <span class="action-text">安全退出</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import api from '@/api/axios';
import { ElMessage } from 'element-plus';
import {
  UserFilled, User, CircleCheck, Key, Monitor, Edit, Message, Calendar, Setting,
  Refresh, QuestionFilled, SwitchButton, Star, TrendCharts, ArrowRight, View,
  Minus, Plus, ArrowUp, ArrowDown, Cpu, Memo, Connection
} from '@element-plus/icons-vue';

const authStore = useAuthStore();
const router = useRouter();
const user = computed(() => authStore.user);

// 响应式数据
const currentDateTime = ref('');
const showAllPermissions = ref(false);

// 统计数据 - 添加动画效果
const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  totalRoles: 0,
  totalPermissions: 0,
  userTrend: '配置稳定',
  permissionTrend: '配置稳定'
});

const animatedStats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  totalRoles: 0,
  totalPermissions: 0
});

// 系统状态数据
const systemStatus = ref({
  cpu: 45,
  memory: 62,
  network: '优秀',
  uptime: '15天 8小时 32分钟',
  status: '运行正常',
  currentTime: ''
});

// 加载状态
const loading = ref({
  stats: false,
  systemStatus: false
});

// 计算属性
const displayedPermissions = computed(() => {
  if (!user.value?.permissions) return [];
  return showAllPermissions.value ? user.value.permissions : user.value.permissions.slice(0, 5);
});

// 方法
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 6) return '深夜好';
  if (hour < 9) return '早上好';
  if (hour < 12) return '上午好';
  if (hour < 14) return '中午好';
  if (hour < 17) return '下午好';
  if (hour < 19) return '傍晚好';
  if (hour < 22) return '晚上好';
  return '深夜好';
};

const updateDateTime = () => {
  const now = new Date();
  currentDateTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    weekday: 'long'
  });
};

const formatJoinDate = () => {
  // 模拟加入日期
  return '2024年1月15日';
};

const getRoleTagType = (role) => {
  const typeMap = {
    'ADMIN': 'danger',
    'MANAGER': 'warning',
    'USER': 'primary'
  };
  return typeMap[role] || 'info';
};

const getPermissionType = (resource) => {
  const typeMap = {
    'user': 'primary',
    'role': 'success',
    'permission': 'warning',
    'system': 'danger',
    'profile': 'info'
  };
  return typeMap[resource] || 'info';
};

const formatRoles = (roles) => {
  if (!roles || roles.length === 0) return '普通用户';
  const roleMap = {
    'ADMIN': '超级管理员',
    'MANAGER': '管理员',
    'USER': '普通用户'
  };
  return roles.map(role => roleMap[role] || role).join(', ');
};

// 数字动画效果
const animateNumber = (target, current, key) => {
  const diff = target - current;
  const step = Math.ceil(diff / 20);
  if (current < target) {
    animatedStats.value[key] = Math.min(current + step, target);
    setTimeout(() => animateNumber(target, animatedStats.value[key], key), 50);
  }
};

const refreshUserInfo = async () => {
  try {
    await authStore.fetchUser();
    console.log('用户信息已刷新');
  } catch (error) {
    console.error('刷新用户信息失败:', error);
  }
};

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};

// 获取统计数据
const fetchDashboardStats = async () => {
  try {
    loading.value.stats = true;
    const response = await api.get('/api/dashboard/stats');
    const data = response.data;
    
    // 更新统计数据
    stats.value = {
      totalUsers: data.totalUsers || 0,
      onlineUsers: data.onlineUsers || 0,
      totalRoles: data.totalRoles || 0,
      totalPermissions: data.totalPermissions || 0,
      userTrend: data.userTrend || '配置稳定',
      permissionTrend: data.permissionTrend || '配置稳定'
    };
    
    // 启动数字动画
    Object.keys(animatedStats.value).forEach(key => {
      if (typeof stats.value[key] === 'number') {
        animateNumber(stats.value[key], 0, key);
      }
    });
    
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
    // 使用默认数据
    stats.value = {
      totalUsers: 0,
      onlineUsers: 0,
      totalRoles: 0,
      totalPermissions: 0,
      userTrend: '数据获取失败',
      permissionTrend: '数据获取失败'
    };
  } finally {
    loading.value.stats = false;
  }
};

// 获取系统状态
const fetchSystemStatus = async () => {
  try {
    loading.value.systemStatus = true;
    const response = await api.get('/api/dashboard/system-status');
    const data = response.data;
    
    systemStatus.value = {
      cpu: data.cpu || 0,
      memory: data.memory || 0,
      network: data.network || '未知',
      uptime: data.uptime || '未知',
      status: data.status || '未知',
      currentTime: data.currentTime || new Date().toLocaleString('zh-CN')
    };
    
  } catch (error) {
    console.error('获取系统状态失败:', error);
    ElMessage.error('获取系统状态失败');
    // 保持默认数据
  } finally {
    loading.value.systemStatus = false;
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    fetchDashboardStats(),
    fetchSystemStatus(),
    refreshUserInfo()
  ]);
  ElMessage.success('数据已刷新');
};

// 定时器
let dateTimeTimer;
let systemStatusTimer;
let dataRefreshTimer;

onMounted(async () => {
  // 初始化时间显示
  updateDateTime();
  dateTimeTimer = setInterval(updateDateTime, 1000);
  
  // 获取初始数据
  await Promise.all([
    fetchDashboardStats(),
    fetchSystemStatus(),
    refreshUserInfo()
  ]);
  
  // 定期刷新系统状态（每30秒）
  systemStatusTimer = setInterval(() => {
    fetchSystemStatus();
  }, 30000);
  
  // 定期刷新统计数据（每5分钟）
  dataRefreshTimer = setInterval(() => {
    fetchDashboardStats();
  }, 300000);
});

onUnmounted(() => {
  if (dateTimeTimer) clearInterval(dateTimeTimer);
  if (systemStatusTimer) clearInterval(systemStatusTimer);
  if (dataRefreshTimer) clearInterval(dataRefreshTimer);
});
</script>

<style scoped>
/* 全局样式 */
.dashboard {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.title-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon-wrapper {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.title-icon {
  font-size: 32px;
  color: white;
}

.title-text {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 4px 0 0 0;
}

.welcome-message {
  text-align: right;
}

.greeting {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.greeting-text {
  font-size: 18px;
  color: #666;
}

.user-display-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.greeting-icon {
  font-size: 20px;
  color: #fbbf24;
}

.current-time {
  font-size: 14px;
  color: #888;
  margin: 0;
}

.header-actions {
  margin-top: 16px;
}

.user-card {
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.user-role-badge {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.role-tag {
  font-size: 12px;
  border-radius: 6px;
}

.online-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 32px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.stats-content {
  padding: 24px;
  position: relative;
}

.stats-icon-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  position: relative;
  z-index: 2;
}

.stats-users .stats-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-online .stats-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-roles .stats-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-permissions .stats-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-bg-pattern {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.pulse-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.stats-number {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.up {
  color: #10b981;
}

.trend-icon.stable {
  color: #f59e0b;
}

.trend-text {
  font-size: 12px;
  color: #666;
}

.real-time-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.live-dot {
  width: 6px;
  height: 6px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.live-text {
  font-size: 12px;
  color: #ef4444;
  font-weight: 500;
}

.stats-footer {
  padding: 12px 24px;
  background: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-text {
  font-size: 12px;
  color: #666;
}

.footer-arrow, .footer-icon {
  font-size: 14px;
  color: #409eff;
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 32px;
}

.content-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #409eff;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.edit-btn {
  font-size: 14px;
}

/* 个人资料卡片 */
.profile-content {
  padding: 24px;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.avatar-info {
  flex: 1;
}

.display-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.username {
  font-size: 14px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
}

.detail-icon {
  font-size: 16px;
  color: #409eff;
  width: 20px;
}

.detail-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.roles-container {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* 权限卡片 */
.permission-count {
  margin-left: 8px;
}

.permissions-section {
  padding: 24px;
}

.permissions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 320px;
  overflow-y: auto;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.permission-item:hover {
  background: rgba(64, 158, 255, 0.05);
  border-color: rgba(64, 158, 255, 0.2);
}

.permission-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.permission-content {
  flex: 1;
  min-width: 0;
}

.permission-name {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.permission-code {
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.permission-badge {
  flex-shrink: 0;
}

.show-more {
  text-align: center;
  padding: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 8px;
}

.empty-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 系统状态卡片 */
.system-content {
  padding: 24px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot.running {
  background: #10b981;
}

.status-text {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.system-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.metric-info {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.network-indicator {
  display: flex;
  gap: 3px;
  align-items: flex-end;
  height: 20px;
}

.signal-bar {
  width: 6px;
  background: #e5e7eb;
  border-radius: 1px;
  transition: background-color 0.3s ease;
}

.signal-bar:nth-child(1) { height: 8px; }
.signal-bar:nth-child(2) { height: 12px; }
.signal-bar:nth-child(3) { height: 16px; }
.signal-bar:nth-child(4) { height: 20px; }

.signal-bar.active {
  background: #10b981;
}

.system-info {
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

/* 快速操作 */
.quick-actions {
  padding: 24px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.5);
  border: 2px dashed rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 120px;
}

.action-item:hover {
  background: rgba(64, 158, 255, 0.05);
  border-color: #409eff;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.logout-action:hover {
  background: rgba(245, 108, 108, 0.05);
  border-color: #f56c6c;
  box-shadow: 0 8px 25px rgba(245, 108, 108, 0.15);
}

.action-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.logout-action .action-icon {
  color: #f56c6c;
}

.action-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  text-align: center;
}

.action-item:hover .action-text {
  color: #409eff;
}

.logout-action:hover .action-text {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .header-content {
    padding: 20px;
  }
  
  .title-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .welcome-message {
    text-align: left;
    margin-top: 16px;
  }
  
  .user-card {
    margin-top: 16px;
  }
  
  .stats-content {
    padding: 20px;
  }
  
  .content-card {
    margin-bottom: 16px;
  }
}
</style>