<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    @close="handleClose"
    class="permission-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon :class="['header-icon', mode]">
          <component :is="headerIcon" />
        </el-icon>
        <span class="header-title">{{ dialogTitle }}</span>
      </div>
    </template>

    <div class="dialog-content">
      <el-alert
        v-if="mode === 'edit' && currentPermission.isSystem"
        type="warning"
        :closable="false"
        show-icon
        class="system-alert"
      >
        <template #title>
          系统权限提示
        </template>
        <template #default>
          此为系统权限，部分字段不可修改。修改系统权限可能会影响系统功能，请谨慎操作。
        </template>
      </el-alert>

      <PermissionForm
        ref="formRef"
        :permission="currentPermission"
        :parent-options="parentOptions"
        :mode="mode"
        :parent-id="parentId"
        @submit="handleSubmit"
        @cancel="handleClose"
      />
    </div>

    <template #footer v-if="mode !== 'view'">
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="submitting"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import {
  Plus, Edit, View, Warning
} from '@element-plus/icons-vue'
import { ElMessage, ElNotification } from 'element-plus'
import PermissionForm from './PermissionForm.vue'
import { permissionService } from '@/api/permissionService'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  permission: {
    type: Object,
    default: () => ({})
  },
  parentOptions: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value) => ['create', 'edit', 'view'].includes(value)
  },
  parentId: {
    type: [Number, String],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success', 'close'])

// Refs
const formRef = ref()
const submitting = ref(false)

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  const titles = {
    create: '新建权限',
    edit: '编辑权限',
    view: '查看权限'
  }
  return titles[props.mode] || '权限管理'
})

const dialogWidth = computed(() => {
  return window.innerWidth < 768 ? '95%' : '800px'
})

const headerIcon = computed(() => {
  const icons = {
    create: Plus,
    edit: Edit,
    view: View
  }
  return icons[props.mode] || Warning
})

const submitButtonText = computed(() => {
  return props.mode === 'create' ? '创建' : '更新'
})

const currentPermission = computed(() => {
  if (props.mode === 'create') {
    return {
      name: '',
      code: '',
      description: '',
      resourceType: 'API',
      resourcePath: '',
      httpMethod: 'ALL',
      parentId: props.parentId,
      level: props.parentId ? 2 : 1,
      sortOrder: 0,
      status: 'ACTIVE',
      isSystem: false
    }
  }
  return props.permission
})

// Methods
const handleSubmit = async (formData) => {
  try {
    submitting.value = true

    // Validate form data
    const validation = permissionService.validatePermissionData(formData)
    if (!validation.isValid) {
      ElMessage.error(validation.errors.join('; '))
      return
    }

    let response
    if (props.mode === 'create') {
      response = await permissionService.createPermission(formData)
      ElNotification.success({
        title: '创建成功',
        message: `权限"${formData.name}"已创建`,
        type: 'success'
      })
    } else if (props.mode === 'edit') {
      response = await permissionService.updatePermission(props.permission.id, formData)
      ElNotification.success({
        title: '更新成功',
        message: `权限"${formData.name}"已更新`,
        type: 'success'
      })
    }

    emit('success', response?.data)
    handleClose()
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error(error.message || (props.mode === 'create' ? '创建权限失败' : '更新权限失败'))
  } finally {
    submitting.value = false
  }
}

const handleConfirm = async () => {
  const isValid = await formRef.value?.validateForm()
  if (isValid) {
    formRef.value?.handleSubmit()
  }
}

const handleClose = () => {
  formRef.value?.resetForm()
  emit('close')
  emit('update:visible', false)
}

// Watchers
watch(() => props.visible, (newVal) => {
  if (newVal && formRef.value) {
    // Reset form when dialog opens
    formRef.value.resetForm()
  }
})

// Resize handler for responsive dialog width
let resizeTimeout
const handleResize = () => {
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    dialogWidth.value = window.innerWidth < 768 ? '95%' : '800px'
  }, 200)
}

// Lifecycle hooks
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  clearTimeout(resizeTimeout)
})
</script>

<style scoped>
.permission-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
}

.header-icon.create { color: var(--el-color-primary); }
.header-icon.edit { color: var(--el-color-warning); }
.header-icon.view { color: var(--el-color-info); }

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.dialog-content {
  padding: 24px;
}

.system-alert {
  margin-bottom: 24px;
}

.dialog-footer {
  padding: 10px 20px 20px;
  text-align: right;
  border-top: 1px solid var(--el-border-color-light);
}

.dialog-footer .el-button {
  min-width: 100px;
  margin-left: 12px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .permission-dialog :deep(.el-dialog__header) {
    padding: 16px;
  }

  .dialog-content {
    padding: 16px;
  }

  .dialog-footer {
    text-align: center;
    padding: 16px;
  }

  .dialog-footer .el-button {
    margin: 0 6px;
  }

  .header-icon {
    font-size: 20px;
  }

  .header-title {
    font-size: 16px;
  }
}
</style>
