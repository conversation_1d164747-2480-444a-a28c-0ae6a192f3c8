<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div class="permission-dialog">
      <!-- 权限搜索和过滤 -->
      <div class="filter-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索权限"
          :prefix-icon="Search"
          clearable
          @input="filterPermissions"
          class="search-input"
        />
        <el-select
          v-model="selectedCategory"
          placeholder="权限分类"
          clearable
          @change="filterPermissions"
          class="category-select"
        >
          <el-option
            v-for="category in categories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </div>

      <!-- 权限树形控件 -->
      <div class="permission-tree-container">
        <el-tree
          ref="permissionTree"
          :data="filteredPermissions"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="defaultExpandedKeys"
          :check-strictly="false"
          @check="handleCheckChange"
        >
          <template #default="{ node, data }">
            <div class="permission-node">
              <span class="permission-icon">
                <el-icon v-if="data.type === 'MENU'"><Menu /></el-icon>
                <el-icon v-else-if="data.type === 'BUTTON'"><Operation /></el-icon>
                <el-icon v-else><Link /></el-icon>
              </span>
              <span class="permission-label">{{ data.name }}</span>
              <span class="permission-code">{{ data.code }}</span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 快速选择按钮 -->
      <div class="quick-actions">
        <el-button-group>
          <el-button size="small" @click="selectAll">全选</el-button>
          <el-button size="small" @click="unselectAll">取消全选</el-button>
          <el-button size="small" @click="expandAll">展开全部</el-button>
          <el-button size="small" @click="collapseAll">收起全部</el-button>
        </el-button-group>
      </div>

      <!-- 权限模板 -->
      <div class="template-section" v-if="showTemplates">
        <h4>权限模板</h4>
        <el-row :gutter="12">
          <el-col :span="8" v-for="template in permissionTemplates" :key="template.code">
            <el-card shadow="hover" class="template-card">
              <template #header>
                <div class="template-header">
                  <span>{{ template.name }}</span>
                  <el-button
                    type="primary"
                    link
                    @click="applyTemplate(template)"
                  >
                    应用
                  </el-button>
                </div>
              </template>
              <div class="template-content">
                <p class="template-description">{{ template.description }}</p>
                <el-tag size="small" type="info">{{ template.permissionCodes.length }} 个权限</el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 已选权限统计 -->
      <div class="selection-stats">
        已选择 {{ selectedCount }} 个权限
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Menu, Operation, Link } from '@element-plus/icons-vue'
import { roleService } from '@/api/roleService'

const props = defineProps({
  modelValue: Boolean,
  role: {
    type: Object,
    required: true
  },
  permissions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const searchKeyword = ref('')
const selectedCategory = ref('')
const permissionTree = ref(null)
const submitLoading = ref(false)

// 树形控件配置
const treeProps = {
  label: 'name',
  children: 'children'
}

// 权限模板
const permissionTemplates = ref([])
const showTemplates = ref(true)

// 计算属性
const title = computed(() => `分配权限 - ${props.role?.name || ''}`)

const categories = computed(() => {
  const categorySet = new Set()
  props.permissions.forEach(permission => {
    if (permission.category) {
      categorySet.add(permission.category)
    }
  })
  return Array.from(categorySet)
})

const filteredPermissions = computed(() => {
  let result = [...props.permissions]
  
  // 关键字搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(permission => 
      permission.name.toLowerCase().includes(keyword) ||
      permission.code.toLowerCase().includes(keyword)
    )
  }
  
  // 分类过滤
  if (selectedCategory.value) {
    result = result.filter(permission => 
      permission.category === selectedCategory.value
    )
  }
  
  return result
})

const selectedCount = computed(() => {
  if (!permissionTree.value) return 0
  return permissionTree.value.getCheckedKeys().length
})

const defaultCheckedKeys = computed(() => {
  return props.role?.permissions?.map(p => p.id) || []
})

const defaultExpandedKeys = computed(() => {
  // 默认展开第一级节点
  return props.permissions
    .filter(p => !p.parentId)
    .map(p => p.id)
})

// 方法
const handleCheckChange = (data, checked) => {
  // 可以在这里添加一些检查逻辑
  console.log('Permission check changed:', data.code, checked)
}

const handleSubmit = async () => {
  try {
    if (!props.role?.id) {
      ElMessage.error('无效的角色ID')
      return
    }

    submitLoading.value = true
    const checkedKeys = permissionTree.value.getCheckedKeys()
    
    await roleService.assignRolePermissions({
      roleId: props.role.id,
      permissionIds: checkedKeys,
      replaceAll: true
    })
    
    ElMessage.success('权限分配成功')
    dialogVisible.value = false
    emit('submit')
  } catch (error) {
    console.error('分配权限失败:', error)
    ElMessage.error(error.response?.data?.message || '分配权限失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClosed = () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
}

const selectAll = () => {
  permissionTree.value?.setCheckedNodes(filteredPermissions.value)
}

const unselectAll = () => {
  permissionTree.value?.setCheckedKeys([])
}

const expandAll = () => {
  filteredPermissions.value.forEach(node => {
    permissionTree.value?.expand(node.id)
  })
}

const collapseAll = () => {
  filteredPermissions.value.forEach(node => {
    permissionTree.value?.collapse(node.id)
  })
}

const filterPermissions = () => {
  // 过滤后自动展开匹配的节点
  if (searchKeyword.value) {
    expandAll()
  }
}

const applyTemplate = async (template) => {
  try {
    // 获取模板对应的权限ID
    const templatePermissions = props.permissions.filter(p => 
      template.permissionCodes.includes(p.code)
    )
    
    // 设置选中状态
    permissionTree.value?.setCheckedKeys(templatePermissions.map(p => p.id))
    
    ElMessage.success(`已应用${template.name}`)
  } catch (error) {
    console.error('应用模板失败:', error)
    ElMessage.error('应用模板失败')
  }
}

// 生命周期钩子
onMounted(async () => {
  try {
    const response = await roleService.getRoleTemplates()
    if (response.success) {
      permissionTemplates.value = response.data
    }
  } catch (error) {
    console.error('获取权限模板失败:', error)
    showTemplates.value = false
  }
})

// 监听搜索条件变化
watch([searchKeyword, selectedCategory], () => {
  filterPermissions()
})
</script>

<style scoped>
.permission-dialog {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-input {
  width: 300px;
}

.category-select {
  width: 200px;
}

.permission-tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
}

.permission-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-label {
  flex: 1;
}

.permission-code {
  color: #909399;
  font-size: 12px;
  font-family: monospace;
}

.quick-actions {
  display: flex;
  justify-content: flex-start;
  margin: 12px 0;
}

.template-section {
  margin-top: 16px;
}

.template-section h4 {
  margin: 0 0 12px 0;
  color: #606266;
}

.template-card {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-description {
  margin: 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.selection-stats {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}
</style>
