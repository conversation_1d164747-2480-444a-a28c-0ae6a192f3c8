package zh.backend.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zh.backend.annotation.RequirePermission;
import zh.backend.annotation.RequireRole;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于验证权限注解
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 公开接口 - 无需权限
     */
    @GetMapping("/public")
    public ResponseEntity<Map<String, Object>> publicEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个公开接口，无需权限");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 需要权限的接口
     */
    @GetMapping("/permission")
    @RequirePermission("USER_LIST")
    public ResponseEntity<Map<String, Object>> permissionEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个需要USER_LIST权限的接口");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 需要角色的接口
     */
    @GetMapping("/role")
    @RequireRole("ADMIN")
    public ResponseEntity<Map<String, Object>> roleEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个需要ADMIN角色的接口");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 需要多个权限的接口（ANY模式）
     */
    @GetMapping("/multiple-permissions-any")
    @RequirePermission(value = {"USER_CREATE", "USER_UPDATE"}, mode = RequirePermission.PermissionMode.ANY)
    public ResponseEntity<Map<String, Object>> multiplePermissionsAny() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个需要USER_CREATE或USER_UPDATE权限的接口（ANY模式）");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 需要多个权限的接口（ALL模式）
     */
    @GetMapping("/multiple-permissions-all")
    @RequirePermission(value = {"USER_CREATE", "USER_UPDATE"}, mode = RequirePermission.PermissionMode.ALL)
    public ResponseEntity<Map<String, Object>> multiplePermissionsAll() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个需要USER_CREATE和USER_UPDATE权限的接口（ALL模式）");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 允许匿名访问的接口
     */
    @GetMapping("/anonymous")
    @RequirePermission(value = "ADMIN_ONLY", allowAnonymous = true)
    public ResponseEntity<Map<String, Object>> anonymousEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "这是一个允许匿名访问的接口");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
}
