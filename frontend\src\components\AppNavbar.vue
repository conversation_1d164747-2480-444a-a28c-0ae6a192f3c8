<template>
  <el-aside :width="sidebarWidth" style="background-color: #ffffff; transition: width 0.3s ease-in-out; display: flex; flex-direction: column;">
    <!-- 侧栏头部 -->
    <div class="sidebar-header">
      <router-link to="/dashboard" class="flex items-center flex-grow overflow-hidden">
        <Building2Icon class="logo-icon" />
        <span v-show="!sidebarCollapsed" class="logo-text">管理系统</span>
      </router-link>
      <el-button text @click="toggleSidebar" class="collapse-button">
        <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
      </el-button>
    </div>

    <!-- Element Plus 导航菜单 -->
    <el-scrollbar class="flex-grow">
      <el-menu
        :default-active="activeRoute"
        class="el-menu-vertical-layout"
        :collapse="sidebarCollapsed"
        :collapse-transition="false"
        background-color="#ffffff"
        text-color="#4b5563"
        active-text-color="#4f46e5"
        router
        style="border-right: none;"
      >
        <!-- 主要功能 -->
        <el-menu-item v-for="tab in mainTabs" :key="tab.id" :index="tab.route">
          <el-icon><component :is="tab.icon" class="h-5 w-5" /></el-icon>
          <template #title>
            <span>{{ tab.name }}</span>
            <el-badge v-if="tab.badge" :value="tab.badge" class="item" style="margin-left: auto; margin-right: 10px;"/>
          </template>
        </el-menu-item>

        <!-- 管理功能 (动态) -->
        <el-sub-menu index="admin-menu" v-if="adminTabs.length > 0">
          <template #title>
            <el-icon><Setting class="h-5 w-5"/></el-icon>
            <span>管理功能</span>
          </template>
          <el-menu-item v-for="tab in adminTabs" :key="tab.id" :index="tab.route">
            <el-icon><component :is="tab.icon" class="h-5 w-5" /></el-icon>
            <template #title>{{ tab.name }}</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- 快速操作 -->
        <el-sub-menu index="quick-actions" v-if="quickActions.length > 0">
          <template #title>
            <el-icon><Operation class="h-5 w-5"/></el-icon>
            <span>快速操作</span>
          </template>
          <el-menu-item v-for="action in quickActions" :key="action.id" :index="action.route || '#'" @click="() => handleQuickActionClick(action)">
            <el-icon><component :is="action.icon" class="h-5 w-5" /></el-icon>
            <template #title>{{ action.name }}</template>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-scrollbar>

    <!-- 侧栏底部用户信息 -->
    <div class="sidebar-footer" v-if="isAuthenticated">
      <div class="flex items-center">
        <div class="user-avatar-wrapper">
          <el-avatar :icon="UserFilled" size="small" :class="getAvatarClass()"/>
          <div class="user-status-indicator" :class="getStatusClass()"></div>
        </div>
        <div v-show="!sidebarCollapsed && user" class="user-info ml-3 flex-1 min-w-0">
          <p class="user-name">{{ user.realName || user.username }}</p>
          <div class="user-roles">
            <el-tag
              v-for="role in getUserRoles()"
              :key="role"
              :type="getRoleTagType(role)"
              effect="light"
              size="small"
              class="role-tag"
            >
              {{ formatRoleName(role) }}
            </el-tag>
            <span v-if="!user.roles || user.roles.length === 0" class="no-role">普通用户</span>
          </div>
        </div>
        <el-dropdown v-show="!sidebarCollapsed" trigger="click" style="margin-left: auto;">
          <el-button text style="padding: 0;">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="$router.push('/profile')">
                <el-icon><UserIcon /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><LogOut /></el-icon>
                安全退出
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-aside>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { getAccessibleAdminMenus } from '@/utils/permissions';
import axios from 'axios';

// Element Plus Icons
import {
  Fold, Expand, Setting, Operation, UserFilled, MoreFilled
} from '@element-plus/icons-vue';

// Lucide Icons
import {
  Building2Icon,
  HomeIcon,
  ShieldIcon,
  SettingsIcon,
  UserIcon,
  UsersIcon,
  RefreshCwIcon,
  LogOut,

} from 'lucide-vue-next';

// Authentication and Router
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();

const isAuthenticated = computed(() => authStore.isAuthenticated);
const user = computed(() => authStore.user);

// Sidebar state
const sidebarCollapsed = ref(false);
const sidebarWidth = computed(() => sidebarCollapsed.value ? '64px' : '200px');

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// Navigation items
const mainTabs = computed(() => {
  const tabs = [
    { id: 'dashboard', name: '仪表盘', icon: HomeIcon, route: '/dashboard' },
    { id: 'profile', name: '个人资料', icon: UserIcon, route: '/profile' },
  ];


  return tabs;
});

// Admin tabs based on permissions
const adminTabs = computed(() => {
  if (!user.value) return [];

  const accessibleMenus = getAccessibleAdminMenus(user.value);

  // 映射图标
  const iconMap = {
    'users': UsersIcon,
    'roles': ShieldIcon,
    'permissions': SettingsIcon
  };

  return accessibleMenus.map(menu => ({
    ...menu,
    icon: iconMap[menu.id] || SettingsIcon
  }));
});

const quickActions = ref([
  { id: 'refresh-token', name: '刷新令牌', icon: RefreshCwIcon, action: () => refreshUserToken() }
]);

// Active route for menu highlighting
const activeRoute = computed(() => route.path);

// User identity display methods
const getUserRoles = () => {
  if (!user.value || !user.value.roles) return [];
  return user.value.roles;
};

const getRoleTagType = (role) => {
  const roleTypeMap = {
    'SUPER_ADMIN': 'danger',
    'SYSTEM_ADMIN': 'warning',
    'USER': 'primary'
  };
  return roleTypeMap[role] || 'info';
};

const formatRoleName = (role) => {
  const roleNameMap = {
    'SUPER_ADMIN': '超级管理员',
    'SYSTEM_ADMIN': '系统管理员',
    'USER': '普通用户'
  };
  return roleNameMap[role] || role;
};

const getAvatarClass = () => {
  if (!user.value || !user.value.roles) return 'avatar-user';

  const roles = user.value.roles;
  if (roles.includes('SUPER_ADMIN')) return 'avatar-admin';
  if (roles.includes('SYSTEM_ADMIN')) return 'avatar-manager';
  return 'avatar-user';
};

const getStatusClass = () => {
  // 简单的在线状态指示
  return 'status-online';
};

// Handlers
const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};

const handleQuickActionClick = (actionItem) => {
  if (actionItem.action) {
    console.log('执行快速操作:', actionItem.name);
    actionItem.action();
  }
};

const refreshUserToken = async () => {
  try {
    const response = await axios.post('/api/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${authStore.token}` }
    });

    if (response.data && response.data.token) {
      authStore.token = response.data.token;
      localStorage.setItem('token', response.data.token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
      console.log('令牌刷新成功');
    }
  } catch (error) {
    console.error('刷新令牌失败:', error.response?.data?.message || error.message);
  }
};
</script>

<style scoped>
/* Sidebar specific styles */
.el-aside {
  background-color: #ffffff !important;
  color: #333;
  border-right: 1px solid #e5e7eb;
  transition: width 0.3s ease-in-out;
}

/* Sidebar Menu Styling */
.el-menu {
  border-right: none !important;
  background-color: transparent !important;
  padding: 8px;
}

.el-menu-item,
.el-sub-menu__title {
  height: 44px;
  line-height: 44px;
  margin: 4px 0;
  border-radius: 6px;
  color: #4b5563;
  transition: background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
}

/* Icons */
.el-menu-item [class^=el-icon],
.el-sub-menu__title [class^=el-icon] {
  margin-right: 10px;
  width: auto;
  font-size: 16px;
  vertical-align: middle;
}

/* Ensure Lucide icons align */
.el-menu-item .lucide,
.el-sub-menu__title .lucide {
  vertical-align: middle;
  margin-right: 10px;
  width: 16px;
  height: 16px;
}

/* Hover state */
.el-menu-item:not(.is-active):hover,
.el-sub-menu:not(.is-active) > .el-sub-menu__title:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

/* Active state */
.el-menu-item.is-active {
  background-color: #eef2ff !important;
  color: #4f46e5 !important;
  font-weight: 500;
}

.el-menu-item.is-active [class^=el-icon],
.el-menu-item.is-active .lucide {
  color: inherit !important;
}

/* Submenu adjustments */
.el-sub-menu.is-active > .el-sub-menu__title {
  color: #4f46e5 !important;
}

.el-menu--inline {
  background-color: transparent !important;
  padding-left: 10px;
}

.el-menu--inline .el-menu-item {
  height: 40px;
  line-height: 40px;
  margin: 2px 0;
  border-radius: 4px;
}

.el-menu--inline .el-menu-item.is-active {
  background: #e0e7ff !important;
  color: #4f46e5 !important;
  font-weight: normal;
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 16px;
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
}

.sidebar-header .logo-icon {
  height: 28px;
  width: 28px;
  color: #6366f1;
  background-color: #e0e7ff;
  border-radius: 6px;
  padding: 4px;
}

.sidebar-header .logo-text {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-header .collapse-button {
  margin-left: auto;
  color: #6b7280;
}

/* Sidebar Footer (User Info) */
.sidebar-footer {
  padding: 16px;
  flex-shrink: 0;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.sidebar-footer .el-avatar {
  margin-right: 12px;
  background-color: #bfdbfe;
  color: #3b82f6;
}

.sidebar-footer .user-info {
  overflow: hidden;
}

.sidebar-footer .user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-footer .user-role {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-footer .el-dropdown .el-button {
  color: #6b7280;
/* User Avatar with Status */
.user-avatar-wrapper {
  position: relative;
  margin-right: 12px;
}

.user-avatar-wrapper .el-avatar {
  transition: all 0.2s ease;
}

.avatar-admin {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  color: white !important;
}

.avatar-manager {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: white !important;
}

.avatar-user {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
}

.user-status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 1;
}

.status-online {
  background-color: #10b981;
  animation: pulse 2s infinite;
}

.status-offline {
  background-color: #6b7280;
}

/* Enhanced User Info */
.sidebar-footer .user-name {
  font-weight: 600 !important;
  margin-bottom: 4px !important;
}

.user-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.role-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  border-radius: 9px;
  padding: 0 6px;
  font-weight: 500;
}

.no-role {
  font-size: 11px;
  color: #9ca3af;
  font-style: italic;
}

.sidebar-footer .el-dropdown .el-button {
  transition: color 0.2s ease !important;
}

.sidebar-footer .el-dropdown .el-button:hover {
  color: #374151 !important;
}

/* Pulse animation for status indicator */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dropdown menu improvements */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 14px;
}
}
</style>