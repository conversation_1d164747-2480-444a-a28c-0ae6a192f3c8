<template>
  <div class="user-manage-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><UserFilled /></el-icon>
          用户管理
        </h1>
        <p class="page-description">管理系统用户，包括创建、编辑、删除和权限分配</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.total || 0 }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.active || 0 }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon inactive">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.inactive || 0 }}</div>
                <div class="stat-label">禁用用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon locked">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.locked || 0 }}</div>
                <div class="stat-label">锁定用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱或姓名"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            @keyup.enter="handleSearch"
            class="search-input"
          />
          <el-select
            v-model="searchForm.status"
            placeholder="用户状态"
            clearable
            @change="handleSearch"
            class="status-select"
          >
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="禁用" value="INACTIVE" />
            <el-option label="锁定" value="LOCKED" />
          </el-select>
          <el-select
            v-model="searchForm.roleId"
            placeholder="用户角色"
            clearable
            @change="handleSearch"
            class="role-select"
          >
            <el-option label="全部角色" value="" />
            <el-option
              v-for="role in roles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
            class="date-picker"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-area">
          <el-button
            type="primary"
            :icon="Plus"
            @click="handleCreate"
            v-can="'USER_CREATE'"
          >
            新增用户
          </el-button>

          <el-dropdown
            v-if="selectedUsers.length > 0"
            @command="handleBatchAction"
            class="batch-dropdown"
          >
            <el-button type="warning">
              批量操作 ({{ selectedUsers.length }})
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="enable" v-can="'USER_STATUS_MANAGE'">
                  <el-icon><CircleCheck /></el-icon>
                  批量启用
                </el-dropdown-item>
                <el-dropdown-item command="disable" v-can="'USER_STATUS_MANAGE'">
                  <el-icon><CircleClose /></el-icon>
                  批量禁用
                </el-dropdown-item>
                <el-dropdown-item command="assignRole" divided v-can="'USER_UPDATE'">
                  <el-icon><UserFilled /></el-icon>
                  批量分配角色
                </el-dropdown-item>
                <el-dropdown-item command="export" v-can="'USER_EXPORT'">
                  <el-icon><Download /></el-icon>
                  导出用户
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided v-can="'USER_DELETE'">
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown @command="handleMoreAction">
            <el-button>
              更多操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="import" v-can="'USER_IMPORT'">
                  <el-icon><Upload /></el-icon>
                  导入用户
                </el-dropdown-item>
                <el-dropdown-item command="template" v-can="'USER_EXPORT'">
                  <el-icon><Document /></el-icon>
                  下载模板
                </el-dropdown-item>
                <el-dropdown-item command="export" v-can="'USER_EXPORT'">
                  <el-icon><Download /></el-icon>
                  导出全部
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="users"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
        class="user-table"
        stripe
        border
        :default-sort="{ prop: 'id', order: 'descending' }"
      >
        <!-- 多选列 -->
        <el-table-column
          type="selection"
          width="55"
          :selectable="isRowSelectable"
          align="center"
        />

        <!-- 用户信息 -->
        <el-table-column label="用户信息" min-width="220" sortable="custom" prop="username">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar
                :src="row.avatarUrl"
                :size="45"
                class="user-avatar"
              >
                {{ row.realName?.charAt(0) || row.username.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="username">
                  {{ row.username }}
                  <el-tag v-if="row.username === 'admin'" type="danger" size="small" class="admin-tag">
                    超管
                  </el-tag>
                </div>
                <div class="real-name">{{ row.realName || '未设置' }}</div>
                <div class="user-id">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 联系信息 -->
        <el-table-column label="联系信息" min-width="200">
          <template #default="{ row }">
            <div class="contact-info">
              <div class="email" v-if="row.email">
                <el-icon><Message /></el-icon>
                <span>{{ row.email }}</span>
              </div>
              <div class="phone" v-if="row.phone">
                <el-icon><Phone /></el-icon>
                <span>{{ row.phone }}</span>
              </div>
              <div class="no-contact" v-if="!row.email && !row.phone">
                <span class="text-muted">未设置联系方式</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 角色 -->
        <el-table-column label="角色" width="220">
          <template #default="{ row }">
            <div class="roles-container">
              <el-tag
                v-for="role in row.roles"
                :key="role.id"
                :type="getRoleTagType(role.code)"
                size="small"
                class="role-tag"
                effect="light"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="!row.roles || row.roles.length === 0" class="no-roles">
                <el-icon><Warning /></el-icon>
                未分配角色
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column label="状态" width="100" align="center" sortable="custom" prop="status">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
              effect="dark"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column label="创建时间" width="160" sortable="custom" prop="createdAt">
          <template #default="{ row }">
            <div class="date-info">
              <div class="date">{{ formatDate(row.createdAt) }}</div>
              <div class="time">{{ formatTime(row.createdAt) }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 最后登录 -->
        <el-table-column label="最后登录" width="160" sortable="custom" prop="lastLoginTime">
          <template #default="{ row }">
            <div class="last-login">
              <div v-if="row.lastLoginTime" class="date-info">
                <div class="date">{{ formatDate(row.lastLoginTime) }}</div>
                <div class="time">{{ formatTime(row.lastLoginTime) }}</div>
              </div>
              <span v-else class="text-muted">从未登录</span>
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  link
                  type="info"
                  :icon="View"
                  size="small"
                  @click="handleView(row)"
                  v-can="'USER_LIST'"
                />
              </el-tooltip>

              <el-tooltip content="编辑" placement="top">
                <el-button
                  link
                  type="primary"
                  :icon="Edit"
                  size="small"
                  @click="handleEdit(row)"
                  v-can="'USER_UPDATE'"
                />
              </el-tooltip>

              <el-tooltip content="分配角色" placement="top">
                <el-button
                  link
                  type="success"
                  :icon="UserFilled"
                  size="small"
                  @click="handleAssignRole(row)"
                  v-can="'USER_UPDATE'"
                />
              </el-tooltip>

              <el-tooltip content="重置密码" placement="top">
                <el-button
                  link
                  type="warning"
                  :icon="Lock"
                  size="small"
                  @click="handleResetPassword(row)"
                  v-can="'USER_RESET_PASSWORD'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>

              <el-tooltip :content="row.status === 'ACTIVE' ? '禁用' : '启用'" placement="top">
                <el-button
                  link
                  :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
                  :icon="row.status === 'ACTIVE' ? CircleClose : CircleCheck"
                  size="small"
                  @click="handleToggleStatus(row)"
                  v-can="'USER_STATUS_MANAGE'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>

              <el-tooltip content="删除" placement="top">
                <el-button
                  link
                  type="danger"
                  :icon="Delete"
                  size="small"
                  @click="handleDelete(row)"
                  v-can="'USER_DELETE'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
        class="user-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="userForm.realName"
                placeholder="请输入真实姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="userForm.email"
                placeholder="请输入邮箱"
                type="email"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="userForm.phone"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="userForm.password"
                placeholder="请输入密码"
                type="password"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="userForm.confirmPassword"
                placeholder="请确认密码"
                type="password"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="userForm.status" placeholder="请选择状态">
                <el-option label="正常" value="ACTIVE" />
                <el-option label="禁用" value="INACTIVE" />
                <el-option label="锁定" value="LOCKED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select
                v-model="userForm.roleIds"
                placeholder="请选择角色"
                multiple
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="role in roles"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordFormRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            placeholder="请输入新密码"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            placeholder="请确认新密码"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleResetPasswordSubmit"
            :loading="resetPasswordLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  UserFilled, User, CircleCheck, CircleClose, Lock, Search, Plus, ArrowDown,
  Refresh, Edit, Delete, Message, Phone, View, Warning, Upload, Download, Document
} from '@element-plus/icons-vue'
import { userService } from '@/api/userService'
import { roleService } from '@/api/roleService'
import { handleApiError } from '@/utils/errorHandler'

// 响应式数据
const loading = ref(false)
const users = ref([])
const roles = ref([])
const selectedUsers = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const userFormRef = ref()

// 重置密码对话框
const resetPasswordVisible = ref(false)
const resetPasswordLoading = ref(false)
const passwordFormRef = ref()
const currentResetUser = ref(null)

// 用户详情对话框
const userDetailVisible = ref(false)
const currentUser = ref(null)

// 角色分配对话框
const roleAssignVisible = ref(false)
const currentAssignUser = ref(null)
const assignRoleIds = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  roleId: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const userStats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  locked: 0
})

// 用户表单数据
const userForm = reactive({
  id: null,
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  status: 'ACTIVE',
  roleIds: [],
  remark: ''
})

// 重置密码表单
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

const passwordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '创建用户'
})

// 方法
const fetchUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status,
      roleId: searchForm.roleId,
      sortBy: 'id',
      sortDir: 'desc'
    }

    // 添加日期范围过滤
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    const response = await userService.getUsers(params)

    if (response.success) {
      users.value = response.data.content || response.data
      pagination.total = response.data.totalElements || response.data.length || 0

      // 获取统计信息
      await fetchUserStats()
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    handleApiError(error)
  } finally {
    loading.value = false
  }
}

const fetchUserStats = async () => {
  try {
    const response = await userService.getUserStats()
    if (response.success) {
      Object.assign(userStats, response.data)
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    // 如果统计接口失败，使用本地计算
    updateStats()
  }
}

const updateStats = () => {
  userStats.total = users.value.length
  userStats.active = users.value.filter(u => u.status === 'ACTIVE').length
  userStats.inactive = users.value.filter(u => u.status === 'INACTIVE').length
  userStats.locked = users.value.filter(u => u.status === 'LOCKED').length
}

const handleSearch = () => {
  pagination.page = 1
  fetchUsers()
}

const handleCreate = () => {
  isEdit.value = false
  resetUserForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  resetUserForm()

  // 填充表单数据
  Object.assign(userForm, {
    id: row.id,
    username: row.username,
    realName: row.realName,
    email: row.email,
    phone: row.phone,
    status: row.status,
    roleIds: row.roles?.map(role => role.id) || []
  })

  dialogVisible.value = true
}

const handleResetPassword = (row) => {
  currentResetUser.value = row
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    realName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 'ACTIVE',
    roleIds: []
  })

  // 清除表单验证
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    submitLoading.value = true

    const userData = { ...userForm }
    delete userData.confirmPassword

    let response
    if (isEdit.value) {
      response = await userService.updateUser(userData.id, userData)
    } else {
      response = await userService.createUser(userData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '用户更新成功' : '用户创建成功')
      dialogVisible.value = false
      fetchUsers()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    handleApiError(error)
  } finally {
    submitLoading.value = false
  }
}

// 查看用户详情
const handleView = async (row) => {
  try {
    const response = await userService.getUserById(row.id)
    if (response.success) {
      currentUser.value = response.data
      userDetailVisible.value = true
    } else {
      ElMessage.error('获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    handleApiError(error)
  }
}

// 分配角色
const handleAssignRole = async (row) => {
  try {
    currentAssignUser.value = row
    assignRoleIds.value = row.roles?.map(role => role.id) || []
    roleAssignVisible.value = true
  } catch (error) {
    console.error('打开角色分配对话框失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleResetPasswordSubmit = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    resetPasswordLoading.value = true

    const response = await userService.resetUserPassword(
      currentResetUser.value.id,
      passwordForm.newPassword
    )

    if (response.success) {
      ElMessage.success('密码重置成功')
      resetPasswordVisible.value = false
    } else {
      ElMessage.error(response.message || '密码重置失败')
    }
  } catch (error) {
    console.error('密码重置失败:', error)
    handleApiError(error)
  } finally {
    resetPasswordLoading.value = false
  }
}

const handleToggleStatus = async (row) => {
  const action = row.status === 'ACTIVE' ? '禁用' : '启用'
  const newStatus = row.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${row.username} 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await userService.updateUserStatus(row.id, newStatus)

    if (response.success) {
      ElMessage.success(`${action}成功`)
      fetchUsers()
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info(`已取消${action}`)
    } else {
      console.error(`${action}用户失败:`, error)
      handleApiError(error)
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${row.username} 吗？此操作不可撤销！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await userService.deleteUser(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      fetchUsers()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消删除')
    } else {
      console.error('删除用户失败:', error)
      handleApiError(error)
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleBatchAction = async (command) => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户')
    return
  }

  const userIds = selectedUsers.value.map(user => user.id)
  const usernames = selectedUsers.value.map(user => user.username).join('、')

  try {
    switch (command) {
      case 'enable':
        await ElMessageBox.confirm(
          `确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`,
          '确认批量启用',
          { type: 'warning' }
        )
        await batchUpdateStatus(userIds, 'ACTIVE')
        ElMessage.success('批量启用成功')
        break

      case 'disable':
        await ElMessageBox.confirm(
          `确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？`,
          '确认批量禁用',
          { type: 'warning' }
        )
        await batchUpdateStatus(userIds, 'INACTIVE')
        ElMessage.success('批量禁用成功')
        break

      case 'delete':
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可撤销！`,
          '确认批量删除',
          { type: 'warning' }
        )
        const response = await userService.deleteUsers(userIds)
        if (response.success) {
          ElMessage.success('批量删除成功')
        } else {
          ElMessage.error(response.message || '批量删除失败')
        }
        break

      case 'assignRole':
        // 打开批量角色分配对话框
        ElMessage.info('批量角色分配功能开发中...')
        break

      case 'export':
        await exportSelectedUsers()
        break

      default:
        ElMessage.info(`批量操作: ${command}`)
    }

    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      handleApiError(error)
    }
  }
}

const batchUpdateStatus = async (userIds, status) => {
  const promises = userIds.map(id => userService.updateUserStatus(id, status))
  await Promise.all(promises)
}

const handleMoreAction = async (command) => {
  try {
    switch (command) {
      case 'import':
        ElMessage.info('用户导入功能开发中...')
        break
      case 'template':
        ElMessage.info('下载导入模板功能开发中...')
        break
      case 'export':
        await exportAllUsers()
        break
      default:
        ElMessage.info(`更多操作: ${command}`)
    }
  } catch (error) {
    console.error('操作失败:', error)
    handleApiError(error)
  }
}

const exportSelectedUsers = async () => {
  ElMessage.info('导出选中用户功能开发中...')
}

const exportAllUsers = async () => {
  ElMessage.info('导出全部用户功能开发中...')
}

const handleRefresh = () => {
  fetchUsers()
}

const isRowSelectable = (row) => {
  return row.username !== 'admin'
}

const getRowClassName = ({ row }) => {
  return row.status !== 'ACTIVE' ? 'disabled-row' : ''
}

const getRoleTagType = (code) => {
  const types = {
    'SUPER_ADMIN': 'danger',
    'ADMIN': 'warning',
    'USER': 'info'
  }
  return types[code] || 'info'
}

const getStatusTagType = (status) => {
  const types = {
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'LOCKED': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'ACTIVE': '正常',
    'INACTIVE': '禁用',
    'LOCKED': '锁定'
  }
  return texts[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '从未登录'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchUsers()
}

const fetchRoles = async () => {
  try {
    const response = await roleService.getActiveRoles()
    if (response.success) {
      roles.value = response.data
    } else {
      ElMessage.error(response.message || '获取角色列表失败')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    handleApiError(error)
  }
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}

// 格式化时间
const formatTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleTimeString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 重置用户表单
const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    realName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 'ACTIVE',
    roleIds: [],
    remark: ''
  })

  // 清除表单验证
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

// 生命周期
onMounted(() => {
  fetchUsers()
  fetchRoles()
})
</script>

<style scoped>
.user-manage-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.active {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.inactive {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.locked {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

/* 工具栏 */
.toolbar-card {
  margin-bottom: 20px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-area {
  display: flex;
  gap: 12px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.status-select,
.role-select {
  width: 150px;
}

.date-picker {
  width: 240px;
}

.action-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格 */
.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.user-table {
  border-radius: 8px;
  overflow: hidden;
}

.user-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.user-table :deep(.disabled-row) {
  background: #fafafa;
  opacity: 0.7;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.real-name {
  font-size: 12px;
  color: #7f8c8d;
}

.user-id {
  font-size: 11px;
  color: #bdc3c7;
  margin-top: 2px;
}

.admin-tag {
  margin-left: 8px;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.email,
.phone {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #5a6c7d;
}

/* 角色标签 */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-tag {
  margin: 0;
}

.no-roles {
  color: #bdc3c7;
  font-size: 12px;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-contact {
  color: #bdc3c7;
  font-size: 12px;
  font-style: italic;
}

.text-muted {
  color: #6c757d;
  font-size: 12px;
}

/* 日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date-info .date {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
}

.date-info .time {
  font-size: 11px;
  color: #7f8c8d;
}

/* 分页信息 */
.pagination-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.total-info {
  font-size: 14px;
  color: #606266;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 最后登录 */
.last-login {
  font-size: 13px;
  color: #5a6c7d;
}

/* 对话框样式 */
.user-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-area {
    flex-wrap: wrap;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .user-manage-container {
    padding: 10px;
  }

  .stats-cards :deep(.el-col) {
    margin-bottom: 10px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .user-form :deep(.el-col) {
    margin-bottom: 10px;
  }
}
</style>
