<template>
  <div class="user-manage-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><UserFilled /></el-icon>
          用户管理
        </h1>
        <p class="page-description">管理系统用户，包括创建、编辑、删除和权限分配</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.total }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.active }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon inactive">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.inactive }}</div>
                <div class="stat-label">禁用用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon locked">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ userStats.locked }}</div>
                <div class="stat-label">锁定用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱或姓名"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            class="search-input"
          />
          <el-select
            v-model="searchForm.status"
            placeholder="用户状态"
            clearable
            @change="handleSearch"
            class="status-select"
          >
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="禁用" value="INACTIVE" />
            <el-option label="锁定" value="LOCKED" />
          </el-select>
          <el-select
            v-model="searchForm.roleId"
            placeholder="用户角色"
            clearable
            @change="handleSearch"
            class="role-select"
          >
            <el-option label="全部角色" value="" />
            <el-option
              v-for="role in roles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </div>

        <!-- 操作按钮 -->
        <div class="action-area">
          <el-button
            type="primary"
            :icon="Plus"
            @click="handleCreate"
            v-can="'USER_CREATE'"
          >
            新增用户
          </el-button>

          <el-dropdown
            v-if="selectedUsers.length > 0"
            @command="handleBatchAction"
            class="batch-dropdown"
          >
            <el-button type="warning">
              批量操作 ({{ selectedUsers.length }})
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="enable" v-can="'USER_STATUS_MANAGE'">
                  <el-icon><CircleCheck /></el-icon>
                  批量启用
                </el-dropdown-item>
                <el-dropdown-item command="disable" v-can="'USER_STATUS_MANAGE'">
                  <el-icon><CircleClose /></el-icon>
                  批量禁用
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided v-can="'USER_DELETE'">
                  <el-icon><Delete /></el-icon>
                  批量删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button :icon="Refresh" @click="fetchUsers">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card">
      <el-table
        :data="users"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
        class="user-table"
      >
        <!-- 多选列 -->
        <el-table-column
          type="selection"
          width="55"
          :selectable="isRowSelectable"
        />

        <!-- 用户信息 -->
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar
                :src="row.avatarUrl"
                :size="40"
                class="user-avatar"
              >
                {{ row.realName?.charAt(0) || row.username.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="real-name">{{ row.realName || '未设置' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 联系信息 -->
        <el-table-column label="联系信息" min-width="180">
          <template #default="{ row }">
            <div class="contact-info">
              <div class="email">
                <el-icon><Message /></el-icon>
                {{ row.email || '未设置' }}
              </div>
              <div class="phone" v-if="row.phone">
                <el-icon><Phone /></el-icon>
                {{ row.phone }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 角色 -->
        <el-table-column label="角色" width="200">
          <template #default="{ row }">
            <div class="roles-container">
              <el-tag
                v-for="role in row.roles"
                :key="role.id"
                :type="getRoleTagType(role.code)"
                size="small"
                class="role-tag"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="!row.roles || row.roles.length === 0" class="no-roles">
                未分配角色
              </span>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 最后登录 -->
        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            <div class="last-login">
              {{ formatDateTime(row.lastLoginTime) }}
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="编辑" placement="top">
                <el-button
                  link
                  type="primary"
                  :icon="Edit"
                  size="small"
                  @click="handleEdit(row)"
                  v-can="'USER_UPDATE'"
                />
              </el-tooltip>

              <el-tooltip content="重置密码" placement="top">
                <el-button
                  link
                  type="warning"
                  :icon="Lock"
                  size="small"
                  @click="handleResetPassword(row)"
                  v-can="'USER_RESET_PASSWORD'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>

              <el-tooltip :content="row.status === 'ACTIVE' ? '禁用' : '启用'" placement="top">
                <el-button
                  link
                  :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
                  :icon="row.status === 'ACTIVE' ? CircleClose : CircleCheck"
                  size="small"
                  @click="handleToggleStatus(row)"
                  v-can="'USER_STATUS_MANAGE'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>

              <el-tooltip content="删除" placement="top">
                <el-button
                  link
                  type="danger"
                  :icon="Delete"
                  size="small"
                  @click="handleDelete(row)"
                  v-can="'USER_DELETE'"
                  v-if="row.username !== 'admin'"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
        class="user-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="userForm.realName"
                placeholder="请输入真实姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="userForm.email"
                placeholder="请输入邮箱"
                type="email"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="userForm.phone"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="userForm.password"
                placeholder="请输入密码"
                type="password"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="userForm.confirmPassword"
                placeholder="请确认密码"
                type="password"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="userForm.status" placeholder="请选择状态">
                <el-option label="正常" value="ACTIVE" />
                <el-option label="禁用" value="INACTIVE" />
                <el-option label="锁定" value="LOCKED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select
                v-model="userForm.roleIds"
                placeholder="请选择角色"
                multiple
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="role in roles"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordFormRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            placeholder="请输入新密码"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            placeholder="请确认新密码"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleResetPasswordSubmit"
            :loading="resetPasswordLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UserFilled, User, CircleCheck, CircleClose, Lock, Search, Plus, ArrowDown,
  Refresh, Edit, Delete, Message, Phone
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const users = ref([])
const roles = ref([])
const selectedUsers = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const userFormRef = ref()

// 重置密码对话框
const resetPasswordVisible = ref(false)
const resetPasswordLoading = ref(false)
const passwordFormRef = ref()
const currentResetUser = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  roleId: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 统计数据
const userStats = reactive({
  total: 0,
  active: 0,
  inactive: 0,
  locked: 0
})

// 用户表单数据
const userForm = reactive({
  id: null,
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  status: 'ACTIVE',
  roleIds: []
})

// 重置密码表单
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

const passwordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '创建用户'
})

// 方法
const fetchUsers = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    users.value = [
      {
        id: 1,
        username: 'admin',
        realName: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        status: 'ACTIVE',
        lastLoginTime: new Date(),
        roles: [{ id: 1, name: '超级管理员', code: 'SUPER_ADMIN' }]
      },
      {
        id: 2,
        username: 'user1',
        realName: '张三',
        email: '<EMAIL>',
        status: 'ACTIVE',
        lastLoginTime: new Date(Date.now() - 86400000),
        roles: [{ id: 2, name: '普通用户', code: 'USER' }]
      }
    ]

    pagination.total = users.value.length
    updateStats()
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  userStats.total = users.value.length
  userStats.active = users.value.filter(u => u.status === 'ACTIVE').length
  userStats.inactive = users.value.filter(u => u.status === 'INACTIVE').length
  userStats.locked = users.value.filter(u => u.status === 'LOCKED').length
}

const handleSearch = () => {
  pagination.page = 1
  fetchUsers()
}

const handleCreate = () => {
  isEdit.value = false
  resetUserForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  resetUserForm()

  // 填充表单数据
  Object.assign(userForm, {
    id: row.id,
    username: row.username,
    realName: row.realName,
    email: row.email,
    phone: row.phone,
    status: row.status,
    roleIds: row.roles?.map(role => role.id) || []
  })

  dialogVisible.value = true
}

const handleResetPassword = (row) => {
  currentResetUser.value = row
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    username: '',
    realName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 'ACTIVE',
    roleIds: []
  })

  // 清除表单验证
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()
    submitLoading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '用户更新成功' : '用户创建成功')
    dialogVisible.value = false
    fetchUsers()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleResetPasswordSubmit = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    resetPasswordLoading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('密码重置成功')
    resetPasswordVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    resetPasswordLoading.value = false
  }
}

const handleToggleStatus = (row) => {
  const action = row.status === 'ACTIVE' ? '禁用' : '启用'
  ElMessage.info(`${action}用户: ${row.username}`)
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户 ${row.username} 吗？此操作不可撤销！`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleBatchAction = (command) => {
  ElMessage.info(`批量操作: ${command}`)
}

const isRowSelectable = (row) => {
  return row.username !== 'admin'
}

const getRowClassName = ({ row }) => {
  return row.status !== 'ACTIVE' ? 'disabled-row' : ''
}

const getRoleTagType = (code) => {
  const types = {
    'SUPER_ADMIN': 'danger',
    'ADMIN': 'warning',
    'USER': 'info'
  }
  return types[code] || 'info'
}

const getStatusTagType = (status) => {
  const types = {
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'LOCKED': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'ACTIVE': '正常',
    'INACTIVE': '禁用',
    'LOCKED': '锁定'
  }
  return texts[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '从未登录'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchUsers()
}

const fetchRoles = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟角色数据
    roles.value = [
      { id: 1, name: '超级管理员', code: 'SUPER_ADMIN' },
      { id: 2, name: '系统管理员', code: 'SYSTEM_ADMIN' },
      { id: 3, name: '普通用户', code: 'USER' }
    ]
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  }
}

// 生命周期
onMounted(() => {
  fetchUsers()
  fetchRoles()
})
</script>

<style scoped>
.user-manage-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.active {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.inactive {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.locked {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

/* 工具栏 */
.toolbar-card {
  margin-bottom: 20px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-area {
  display: flex;
  gap: 12px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.status-select,
.role-select {
  width: 150px;
}

.action-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格 */
.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.user-table {
  border-radius: 8px;
  overflow: hidden;
}

.user-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.user-table :deep(.disabled-row) {
  background: #fafafa;
  opacity: 0.7;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.real-name {
  font-size: 12px;
  color: #7f8c8d;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.email,
.phone {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #5a6c7d;
}

/* 角色标签 */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-tag {
  margin: 0;
}

.no-roles {
  color: #bdc3c7;
  font-size: 12px;
  font-style: italic;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 最后登录 */
.last-login {
  font-size: 13px;
  color: #5a6c7d;
}

/* 对话框样式 */
.user-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-area {
    flex-wrap: wrap;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .user-manage-container {
    padding: 10px;
  }

  .stats-cards :deep(.el-col) {
    margin-bottom: 10px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .user-form :deep(.el-col) {
    margin-bottom: 10px;
  }
}
</style>
