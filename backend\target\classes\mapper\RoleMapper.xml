<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zh.backend.mapper.RoleMapper">

    <!-- Role ResultMap -->
    <resultMap id="RoleResultMap" type="zh.backend.entity.Role">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="isSystem" column="is_system"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <!-- 根据ID查找角色 -->
    <select id="findById" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE id = #{id}
    </select>

    <!-- 根据角色编码查找角色 -->
    <select id="findByCode" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE code = #{code}
    </select>

    <!-- 根据角色名称查找角色 -->
    <select id="findByName" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE name = #{name}
    </select>

    <!-- 检查角色编码是否存在 -->
    <select id="existsByCode" resultType="boolean">
        SELECT COUNT(*) > 0 FROM roles WHERE code = #{code}
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="existsByName" resultType="boolean">
        SELECT COUNT(*) > 0 FROM roles WHERE name = #{name}
    </select>

    <!-- 查找所有角色 -->
    <select id="findAll" resultMap="RoleResultMap">
        SELECT * FROM roles ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 根据状态查找角色 -->
    <select id="findByStatus" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE status = #{status}
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 查找系统内置角色 -->
    <select id="findByIsSystemTrue" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE is_system = true
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 查找非系统角色 -->
    <select id="findByIsSystemFalse" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE is_system = false
        ORDER BY sort_order ASC, name ASC
    </select>

    <!-- 分页查询角色（支持关键字搜索） -->
    <select id="findByKeyword" resultMap="RoleResultMap">
        SELECT * FROM roles 
        <where>
            <if test="keyword != null and keyword != ''">
                (name LIKE CONCAT('%', #{keyword}, '%') 
                OR code LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="sortBy == 'name'">name</when>
            <when test="sortBy == 'code'">code</when>
            <when test="sortBy == 'createdAt'">created_at</when>
            <when test="sortBy == 'updatedAt'">updated_at</when>
            <when test="sortBy == 'sortOrder'">sort_order</when>
            <otherwise>id</otherwise>
        </choose>
        <choose>
            <when test="sortDir == 'desc'">DESC</when>
            <otherwise>ASC</otherwise>
        </choose>
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 统计关键字搜索结果数量 -->
    <select id="countByKeyword" resultType="long">
        SELECT COUNT(*) FROM roles 
        <where>
            <if test="keyword != null and keyword != ''">
                (name LIKE CONCAT('%', #{keyword}, '%') 
                OR code LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 根据用户ID查找角色 -->
    <select id="findByUserId" resultMap="RoleResultMap">
        SELECT r.* FROM roles r 
        JOIN user_roles ur ON r.id = ur.role_id 
        WHERE ur.user_id = #{userId}
        ORDER BY r.sort_order ASC, r.name ASC
    </select>

    <!-- 根据权限ID查找角色 -->
    <select id="findByPermissionId" resultMap="RoleResultMap">
        SELECT r.* FROM roles r 
        JOIN role_permissions rp ON r.id = rp.role_id 
        WHERE rp.permission_id = #{permissionId}
        ORDER BY r.sort_order ASC, r.name ASC
    </select>

    <!-- 统计角色数量 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM roles
    </select>

    <!-- 根据状态统计角色数量 -->
    <select id="countByStatus" resultType="long">
        SELECT COUNT(*) FROM roles WHERE status = #{status}
    </select>

    <!-- 插入角色 -->
    <insert id="insertRole" parameterType="zh.backend.entity.Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO roles (
            name, code, description, status, is_system, sort_order,
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{name}, #{code}, #{description}, #{status}, #{isSystem}, #{sortOrder},
            #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 更新角色 -->
    <update id="updateRole" parameterType="zh.backend.entity.Role">
        UPDATE roles SET
            name = #{name},
            code = #{code},
            description = #{description},
            status = #{status},
            is_system = #{isSystem},
            sort_order = #{sortOrder},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除角色 -->
    <delete id="deleteById">
        DELETE FROM roles WHERE id = #{id}
    </delete>

    <!-- 批量删除角色 -->
    <delete id="deleteByIds">
        DELETE FROM roles WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 为角色分配权限 -->
    <insert id="assignPermissionToRole">
        INSERT IGNORE INTO role_permissions (role_id, permission_id) 
        VALUES (#{roleId}, #{permissionId})
    </insert>

    <!-- 移除角色的权限 -->
    <delete id="removePermissionFromRole">
        DELETE FROM role_permissions 
        WHERE role_id = #{roleId} AND permission_id = #{permissionId}
    </delete>

    <!-- 清除角色的所有权限 -->
    <delete id="clearRolePermissions">
        DELETE FROM role_permissions WHERE role_id = #{roleId}
    </delete>

    <!-- 为用户分配角色 -->
    <insert id="assignRoleToUser">
        INSERT IGNORE INTO user_roles (user_id, role_id) 
        VALUES (#{userId}, #{roleId})
    </insert>

    <!-- 移除用户的角色 -->
    <delete id="removeRoleFromUser">
        DELETE FROM user_roles 
        WHERE user_id = #{userId} AND role_id = #{roleId}
    </delete>

    <!-- 清除用户的所有角色 -->
    <delete id="clearUserRoles">
        DELETE FROM user_roles WHERE user_id = #{userId}
    </delete>

    <!-- 批量为用户分配角色 -->
    <insert id="batchAssignRolesToUser">
        INSERT IGNORE INTO user_roles (user_id, role_id) VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userId}, #{roleId})
        </foreach>
    </insert>

    <!-- 批量为角色分配权限 -->
    <insert id="batchAssignPermissionsToRole">
        INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
        <foreach collection="permissionIds" item="permissionId" separator=",">
            (#{roleId}, #{permissionId})
        </foreach>
    </insert>

</mapper>
