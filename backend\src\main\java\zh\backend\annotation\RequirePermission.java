package zh.backend.annotation;

import java.lang.annotation.*;

/**
 * 权限验证注解
 * 用于方法级别的权限控制
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {

    /**
     * 需要的权限编码
     */
    String[] value() default {};

    /**
     * 权限编码（与value等价，提供更明确的语义）
     */
    String[] permissions() default {};

    /**
     * 权限验证模式
     */
    PermissionMode mode() default PermissionMode.ANY;

    /**
     * 是否允许匿名访问
     */
    boolean allowAnonymous() default false;

    /**
     * 权限验证失败时的错误消息
     */
    String message() default "权限不足，无法访问该资源";

    /**
     * 权限验证模式枚举
     */
    enum PermissionMode {
        /**
         * 任意一个权限满足即可
         */
        ANY,
        
        /**
         * 所有权限都必须满足
         */
        ALL
    }
}
