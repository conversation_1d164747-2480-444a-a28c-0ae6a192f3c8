<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zh.backend.mapper.UserPermissionMapper">

    <!-- Permission ResultMap -->
    <resultMap id="PermissionResultMap" type="zh.backend.entity.Permission">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="description" column="description"/>
        <result property="resourceType" column="resource_type"/>
        <result property="resourcePath" column="resource_path"/>
        <result property="httpMethod" column="http_method"/>
        <result property="parentId" column="parent_id"/>
        <result property="level" column="level"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="isSystem" column="is_system"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <!-- UserPermission ResultMap -->
    <resultMap id="UserPermissionResultMap" type="zh.backend.entity.UserPermission">
        <id property="id" column="up_id"/>
        <result property="permissionType" column="permission_type"/>
        <result property="grantedAt" column="granted_at"/>
        <result property="grantedBy" column="granted_by"/>
        <result property="expiresAt" column="expires_at"/>
        <result property="status" column="up_status"/>
        <result property="createdAt" column="up_created_at"/>
        <result property="updatedAt" column="up_updated_at"/>
        <result property="createdBy" column="up_created_by"/>
        <result property="updatedBy" column="up_updated_by"/>
        <association property="user" javaType="zh.backend.entity.User">
            <id property="id" column="user_id"/>
            <result property="username" column="username"/>
            <result property="email" column="email"/>
            <result property="realName" column="real_name"/>
        </association>
        <association property="permission" javaType="zh.backend.entity.Permission" resultMap="PermissionResultMap"/>
    </resultMap>

    <!-- 根据用户ID查找激活的权限 -->
    <select id="findActivePermissionsByUserId" resultMap="PermissionResultMap">
        SELECT
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.user_id = #{userId}
            AND up.status = 'ACTIVE'
            AND up.permission_type = 'GRANT'
    </select>

    <!-- 根据用户ID查找激活的用户权限关联 -->
    <select id="findActiveByUserId" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.user_id = #{userId}
            AND up.status = 'ACTIVE'
    </select>

    <!-- 根据用户ID和权限类型查找用户权限关联 -->
    <select id="findByUserIdAndPermissionType" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.user_id = #{userId}
            AND up.permission_type = #{permissionType}
            AND up.status = 'ACTIVE'
    </select>

    <!-- 检查用户是否拥有指定权限 -->
    <select id="existsByUserIdAndPermissionId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user_permissions up
        WHERE up.user_id = #{userId}
            AND up.permission_id = #{permissionId}
            AND up.status = 'ACTIVE'
            AND up.permission_type = 'GRANT'
    </select>

    <!-- 统计用户的直接权限数量 -->
    <select id="countActivePermissionsByUserId" resultType="long">
        SELECT COUNT(*)
        FROM user_permissions up
        WHERE up.user_id = #{userId}
            AND up.status = 'ACTIVE'
            AND up.permission_type = 'GRANT'
    </select>

    <!-- 查找即将过期的用户权限 -->
    <select id="findExpiredPermissions" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.expires_at IS NOT NULL
            AND up.expires_at &lt;= NOW()
            AND up.status = 'ACTIVE'
    </select>

    <!-- 根据用户ID和权限ID查找用户权限关联 -->
    <select id="findByUserIdAndPermissionId" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.user_id = #{userId}
            AND up.permission_id = #{permissionId}
    </select>

    <!-- 根据用户ID查找所有用户权限关联 -->
    <select id="findByUserId" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.user_id = #{userId}
    </select>

    <!-- 根据权限ID查找所有用户权限关联 -->
    <select id="findByPermissionId" resultMap="UserPermissionResultMap">
        SELECT
            up.id as up_id,
            up.permission_type,
            up.granted_at,
            up.granted_by,
            up.expires_at,
            up.status as up_status,
            up.created_at as up_created_at,
            up.updated_at as up_updated_at,
            up.created_by as up_created_by,
            up.updated_by as up_updated_by,
            u.id as user_id,
            u.username,
            u.email,
            u.real_name,
            p.id,
            p.code,
            p.name,
            p.description,
            p.resource_type,
            p.resource_path,
            p.http_method,
            p.parent_id,
            p.level,
            p.sort_order,
            p.is_system,
            p.status,
            p.created_at,
            p.updated_at,
            p.created_by,
            p.updated_by
        FROM user_permissions up
        JOIN users u ON u.id = up.user_id
        JOIN permissions p ON p.id = up.permission_id
        WHERE up.permission_id = #{permissionId}
    </select>

    <!-- 插入用户权限关联 -->
    <insert id="insertUserPermission" parameterType="zh.backend.entity.UserPermission" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_permissions (
            user_id,
            permission_id,
            permission_type,
            granted_at,
            granted_by,
            expires_at,
            status,
            created_at,
            updated_at,
            created_by,
            updated_by
        ) VALUES (
            #{user.id},
            #{permission.id},
            #{permissionType},
            #{grantedAt},
            #{grantedBy},
            #{expiresAt},
            #{status},
            #{createdAt},
            #{updatedAt},
            #{createdBy},
            #{updatedBy}
        )
    </insert>

    <!-- 更新用户权限关联 -->
    <update id="updateUserPermission" parameterType="zh.backend.entity.UserPermission">
        UPDATE user_permissions
        SET permission_type = #{permissionType},
            granted_at = #{grantedAt},
            granted_by = #{grantedBy},
            expires_at = #{expiresAt},
            status = #{status},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据用户ID删除所有用户权限关联 -->
    <delete id="deleteByUserId">
        DELETE FROM user_permissions WHERE user_id = #{userId}
    </delete>

    <!-- 根据权限ID删除所有用户权限关联 -->
    <delete id="deleteByPermissionId">
        DELETE FROM user_permissions WHERE permission_id = #{permissionId}
    </delete>

</mapper>
