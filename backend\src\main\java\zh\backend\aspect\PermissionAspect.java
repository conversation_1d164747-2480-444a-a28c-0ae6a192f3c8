package zh.backend.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import zh.backend.annotation.RequirePermission;
import zh.backend.annotation.RequireRole;
import zh.backend.entity.User;
import zh.backend.repository.UserRepository;
import zh.backend.service.PermissionService;

import java.lang.reflect.Method;

/**
 * 权限验证切面
 */
@Aspect
@Component
public class PermissionAspect {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserRepository userRepository;

    /**
     * 权限验证切面
     */
    @Around("@annotation(zh.backend.annotation.RequirePermission) || @within(zh.backend.annotation.RequirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的注解，如果没有则获取类上的注解
        RequirePermission annotation = method.getAnnotation(RequirePermission.class);
        if (annotation == null) {
            annotation = method.getDeclaringClass().getAnnotation(RequirePermission.class);
        }

        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 检查是否允许匿名访问
        if (annotation.allowAnonymous()) {
            return joinPoint.proceed();
        }

        // 获取当前用户
        String currentUsername = getCurrentUsername();
        if (currentUsername == null) {
            throw new SecurityException("用户未登录");
        }

        // 获取需要验证的权限
        String[] permissions = annotation.permissions().length > 0 ?
            annotation.permissions() : annotation.value();

        if (permissions.length == 0) {
            return joinPoint.proceed();
        }

        // 获取用户信息
        User user = userRepository.findByUsernameWithRolesAndPermissions(currentUsername)
            .orElseThrow(() -> new SecurityException("用户不存在: " + currentUsername));

        // 获取权限服务
        PermissionService permissionService = applicationContext.getBean(PermissionService.class);

        // 验证权限
        boolean hasPermission = false;
        if (annotation.mode() == RequirePermission.PermissionMode.ALL) {
            // 需要拥有所有权限
            hasPermission = permissionService.hasAllPermissions(user.getId(), permissions);
        } else {
            // 只需要拥有任一权限
            hasPermission = permissionService.hasAnyPermission(user.getId(), permissions);
        }

        if (!hasPermission) {
            throw new SecurityException(annotation.message());
        }

        System.out.println("权限验证通过: 用户 " + currentUsername + " 访问权限: " + String.join(",", permissions));

        return joinPoint.proceed();
    }

    /**
     * 角色验证切面
     */
    @Around("@annotation(zh.backend.annotation.RequireRole) || @within(zh.backend.annotation.RequireRole)")
    public Object checkRole(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的注解，如果没有则获取类上的注解
        RequireRole annotation = method.getAnnotation(RequireRole.class);
        if (annotation == null) {
            annotation = method.getDeclaringClass().getAnnotation(RequireRole.class);
        }

        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 检查是否允许匿名访问
        if (annotation.allowAnonymous()) {
            return joinPoint.proceed();
        }

        // 获取当前用户
        String currentUsername = getCurrentUsername();
        if (currentUsername == null) {
            throw new SecurityException("用户未登录");
        }

        // 获取需要验证的角色
        String[] roles = annotation.roles().length > 0 ?
            annotation.roles() : annotation.value();

        if (roles.length == 0) {
            return joinPoint.proceed();
        }

        // 获取用户信息
        User user = userRepository.findByUsernameWithRolesAndPermissions(currentUsername)
            .orElseThrow(() -> new SecurityException("用户不存在: " + currentUsername));

        // 验证角色
        boolean hasRole = false;
        if (annotation.mode() == RequireRole.RoleMode.ALL) {
            // 需要拥有所有角色
            hasRole = user.getRoles().stream()
                .map(role -> role.getCode())
                .collect(java.util.stream.Collectors.toSet())
                .containsAll(java.util.Arrays.asList(roles));
        } else {
            // 只需要拥有任一角色
            hasRole = user.getRoles().stream()
                .anyMatch(role -> java.util.Arrays.asList(roles).contains(role.getCode()));
        }

        if (!hasRole) {
            throw new SecurityException(annotation.message());
        }

        System.out.println("角色验证通过: 用户 " + currentUsername + " 拥有角色: " + String.join(",", roles));

        return joinPoint.proceed();
    }

    /**
     * 获取当前登录用户名
     */
    private String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        if ("anonymousUser".equals(username)) {
            return null;
        }

        return username;
    }
}
