import './assets/main.css'
import './assets/theme.css'
// 引入 Element Plus 样式
import 'element-plus/dist/index.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import axios from 'axios'

import App from './App.vue'
import router from './router'
import api from './api/axios'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 导入 auth store 并初始化
// 确保在挂载应用前，认证状态已尽可能从 localStorage 恢复
// 这样路由守卫在首次导航时就能获取到正确的认证状态
import { useAuthStore } from './stores/auth' // 确保路径正确
const authStore = useAuthStore(pinia) // 将 pinia 实例传递给 useAuthStore
authStore.initializeAuth();

// 导入并注册自定义指令
import { canDirective } from './directives/permission'; // 确保路径正确
app.directive('can', canDirective);

app.mount('#app')
