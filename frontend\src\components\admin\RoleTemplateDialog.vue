<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择角色模板"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="template-dialog">
      <div class="template-description">
        <p>选择一个角色模板来快速创建角色，模板包含预定义的权限配置。</p>
      </div>

      <div class="template-grid">
        <div
          v-for="template in templates"
          :key="template.code"
          class="template-item"
          :class="{ 'selected': selectedTemplate?.code === template.code }"
          @click="selectTemplate(template)"
        >
          <div class="template-header">
            <div class="template-icon">
              <el-icon v-if="template.category === '系统管理'"><Setting /></el-icon>
              <el-icon v-else-if="template.category === '内容管理'"><Document /></el-icon>
              <el-icon v-else><User /></el-icon>
            </div>
            <div class="template-info">
              <h4 class="template-name">{{ template.name }}</h4>
              <span class="template-category">{{ template.category }}</span>
            </div>
            <div class="template-status">
              <el-icon v-if="selectedTemplate?.code === template.code" class="check-icon">
                <Check />
              </el-icon>
            </div>
          </div>
          
          <div class="template-content">
            <p class="template-description">{{ template.description }}</p>
            
            <div class="template-permissions">
              <h5>包含权限：</h5>
              <div class="permission-tags">
                <el-tag
                  v-for="permissionCode in template.permissionCodes.slice(0, 3)"
                  :key="permissionCode"
                  size="small"
                  type="info"
                >
                  {{ getPermissionName(permissionCode) }}
                </el-tag>
                <el-tag
                  v-if="template.permissionCodes.length > 3"
                  size="small"
                  type="info"
                >
                  +{{ template.permissionCodes.length - 3 }} 个
                </el-tag>
              </div>
            </div>

            <div class="template-meta">
              <span class="permission-count">
                {{ template.permissionCodes.length }} 个权限
              </span>
              <el-tag v-if="template.isBuiltIn" size="small" type="warning">
                内置模板
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义角色选项 -->
      <div class="custom-option">
        <div
          class="template-item custom-template"
          :class="{ 'selected': selectedTemplate?.code === 'CUSTOM' }"
          @click="selectCustom"
        >
          <div class="template-header">
            <div class="template-icon custom">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="template-info">
              <h4 class="template-name">自定义角色</h4>
              <span class="template-category">自定义配置</span>
            </div>
            <div class="template-status">
              <el-icon v-if="selectedTemplate?.code === 'CUSTOM'" class="check-icon">
                <Check />
              </el-icon>
            </div>
          </div>
          <div class="template-content">
            <p class="template-description">创建一个空白角色，稍后手动配置权限。</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedTemplate"
        >
          确认选择
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Document, User, Plus, Check } from '@element-plus/icons-vue'
import { roleService } from '@/api/roleService'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'template-selected'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const templates = ref([])
const selectedTemplate = ref(null)

// 权限名称映射（简化版）
const permissionNameMap = {
  'USER_MANAGEMENT': '用户管理',
  'ROLE_MANAGEMENT': '角色管理',
  'PERMISSION_MANAGEMENT': '权限管理',
  'CONTENT_CREATE': '内容创建',
  'CONTENT_UPDATE': '内容更新',
  'CONTENT_DELETE': '内容删除',
  'CONTENT_VIEW': '内容查看'
}

// 方法
const getPermissionName = (code) => {
  return permissionNameMap[code] || code
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
}

const selectCustom = () => {
  selectedTemplate.value = {
    code: 'CUSTOM',
    name: '自定义角色',
    description: '创建空白角色',
    category: '自定义',
    permissionCodes: [],
    isBuiltIn: false
  }
}

const handleConfirm = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请选择一个模板')
    return
  }

  emit('template-selected', selectedTemplate.value)
  dialogVisible.value = false
  selectedTemplate.value = null
}

// 生命周期
onMounted(async () => {
  try {
    const response = await roleService.getRoleTemplates()
    if (response.success) {
      templates.value = response.data
    }
  } catch (error) {
    console.error('获取角色模板失败:', error)
    ElMessage.error('获取角色模板失败')
  }
})
</script>

<style scoped>
.template-dialog {
  min-height: 400px;
}

.template-description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.template-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.template-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.template-icon.custom {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.template-info {
  flex: 1;
}

.template-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.template-category {
  font-size: 12px;
  color: #909399;
}

.template-status {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #67c23a;
  font-size: 18px;
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.template-permissions h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #2c3e50;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.permission-count {
  font-size: 12px;
  color: #909399;
}

.custom-option {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.custom-template {
  border-style: dashed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
  }
}
</style>
