package zh.backend.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import zh.backend.entity.Permission;
import zh.backend.entity.Role;
import zh.backend.entity.User;
import zh.backend.mapper.PermissionMapper;
import zh.backend.mapper.RoleMapper;
import zh.backend.mapper.UserMapper;

import java.util.Arrays;
import java.util.HashSet;

/**
 * 数据初始化器
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        // 检查是否已经初始化过数据
        if (userMapper.count() > 0) {
            return;
        }

        // 初始化权限
        initPermissions();

        // 初始化角色
        initRoles();

        // 初始化用户
        initUsers();
    }

    private void initPermissions() {
        // 1. 用户管理权限
        Permission userManagement = createPermission("用户管理", "USER_MANAGEMENT", "用户管理模块", Permission.ResourceType.MENU, "/admin/users", Permission.HttpMethod.ALL, null, 1, 1, true);
        createPermission("查看用户列表", "USER_LIST", "查看用户列表", Permission.ResourceType.API, "/api/users", Permission.HttpMethod.GET, userManagement.getId(), 2, 1, true);
        createPermission("创建用户", "USER_CREATE", "创建新用户", Permission.ResourceType.API, "/api/users", Permission.HttpMethod.POST, userManagement.getId(), 2, 2, true);
        createPermission("编辑用户", "USER_UPDATE", "编辑用户信息", Permission.ResourceType.API, "/api/users/*", Permission.HttpMethod.PUT, userManagement.getId(), 2, 3, true);
        createPermission("删除用户", "USER_DELETE", "删除用户", Permission.ResourceType.API, "/api/users/*", Permission.HttpMethod.DELETE, userManagement.getId(), 2, 4, true);
        createPermission("重置用户密码", "USER_RESET_PASSWORD", "重置用户密码", Permission.ResourceType.API, "/api/users/*/reset-password", Permission.HttpMethod.POST, userManagement.getId(), 2, 5, true);
        createPermission("用户状态管理", "USER_STATUS_MANAGE", "管理用户状态", Permission.ResourceType.API, "/api/users/*/status", Permission.HttpMethod.PATCH, userManagement.getId(), 2, 6, true);
        createPermission("用户角色分配", "USER_ROLE_ASSIGN", "为用户分配角色", Permission.ResourceType.API, "/api/users/*/roles", Permission.HttpMethod.POST, userManagement.getId(), 2, 7, true);

        // 2. 角色管理权限
        Permission roleManagement = createPermission("角色管理", "ROLE_MANAGEMENT", "角色管理模块", Permission.ResourceType.MENU, "/admin/roles", Permission.HttpMethod.ALL, null, 1, 2, true);
        createPermission("查看角色列表", "ROLE_LIST", "查看角色列表", Permission.ResourceType.API, "/api/roles", Permission.HttpMethod.GET, roleManagement.getId(), 2, 1, true);
        createPermission("创建角色", "ROLE_CREATE", "创建新角色", Permission.ResourceType.API, "/api/roles", Permission.HttpMethod.POST, roleManagement.getId(), 2, 2, true);
        createPermission("编辑角色", "ROLE_UPDATE", "编辑角色信息", Permission.ResourceType.API, "/api/roles/*", Permission.HttpMethod.PUT, roleManagement.getId(), 2, 3, true);
        createPermission("删除角色", "ROLE_DELETE", "删除角色", Permission.ResourceType.API, "/api/roles/*", Permission.HttpMethod.DELETE, roleManagement.getId(), 2, 4, true);
        createPermission("分配角色权限", "ROLE_ASSIGN_PERMISSIONS", "为角色分配权限", Permission.ResourceType.API, "/api/roles/*/permissions", Permission.HttpMethod.POST, roleManagement.getId(), 2, 5, true);
        createPermission("角色用户管理", "ROLE_USER_MANAGE", "管理角色下的用户", Permission.ResourceType.API, "/api/roles/*/users", Permission.HttpMethod.POST, roleManagement.getId(), 2, 6, true);

        // 3. 权限管理权限
        Permission permissionManagement = createPermission("权限管理", "PERMISSION_MANAGEMENT", "权限管理模块", Permission.ResourceType.MENU, "/admin/permissions", Permission.HttpMethod.ALL, null, 1, 3, true);
        createPermission("查看权限列表", "PERMISSION_LIST", "查看权限列表", Permission.ResourceType.API, "/api/permissions", Permission.HttpMethod.GET, permissionManagement.getId(), 2, 1, true);
        createPermission("创建权限", "PERMISSION_CREATE", "创建新权限", Permission.ResourceType.API, "/api/permissions", Permission.HttpMethod.POST, permissionManagement.getId(), 2, 2, true);
        createPermission("编辑权限", "PERMISSION_UPDATE", "编辑权限信息", Permission.ResourceType.API, "/api/permissions/*", Permission.HttpMethod.PUT, permissionManagement.getId(), 2, 3, true);
        createPermission("删除权限", "PERMISSION_DELETE", "删除权限", Permission.ResourceType.API, "/api/permissions/*", Permission.HttpMethod.DELETE, permissionManagement.getId(), 2, 4, true);
        createPermission("权限树查看", "PERMISSION_TREE", "查看权限树形结构", Permission.ResourceType.API, "/api/permissions/tree", Permission.HttpMethod.GET, permissionManagement.getId(), 2, 5, true);

        // 4. 系统监控权限
        Permission systemMonitor = createPermission("系统监控", "SYSTEM_MONITOR", "系统监控模块", Permission.ResourceType.MENU, "/admin/monitor", Permission.HttpMethod.ALL, null, 1, 4, true);
        createPermission("查看系统日志", "LOG_VIEW", "查看系统操作日志", Permission.ResourceType.API, "/api/logs", Permission.HttpMethod.GET, systemMonitor.getId(), 2, 1, true);
        createPermission("在线用户管理", "ONLINE_USERS", "查看和管理在线用户", Permission.ResourceType.API, "/api/online-users", Permission.HttpMethod.GET, systemMonitor.getId(), 2, 2, true);
        createPermission("系统健康检查", "HEALTH_CHECK", "查看系统健康状态", Permission.ResourceType.API, "/api/health", Permission.HttpMethod.GET, systemMonitor.getId(), 2, 3, true);

        // 5. 个人中心权限
        Permission profile = createPermission("个人中心", "PROFILE", "个人中心模块", Permission.ResourceType.MENU, "/profile", Permission.HttpMethod.ALL, null, 1, 5, true);
        createPermission("查看个人信息", "PROFILE_VIEW", "查看个人信息", Permission.ResourceType.API, "/api/profile", Permission.HttpMethod.GET, profile.getId(), 2, 1, true);
        createPermission("修改个人信息", "PROFILE_UPDATE", "修改个人信息", Permission.ResourceType.API, "/api/profile", Permission.HttpMethod.PUT, profile.getId(), 2, 2, true);
        createPermission("修改密码", "PASSWORD_CHANGE", "修改个人密码", Permission.ResourceType.API, "/api/profile/password", Permission.HttpMethod.PUT, profile.getId(), 2, 3, true);

        // 6. 仪表盘权限
        Permission dashboard = createPermission("仪表盘", "DASHBOARD", "仪表盘模块", Permission.ResourceType.MENU, "/dashboard", Permission.HttpMethod.ALL, null, 1, 6, true);
        createPermission("查看统计数据", "DASHBOARD_STATS", "查看仪表盘统计数据", Permission.ResourceType.API, "/api/dashboard/stats", Permission.HttpMethod.GET, dashboard.getId(), 2, 1, true);
        createPermission("查看系统状态", "DASHBOARD_SYSTEM_STATUS", "查看系统运行状态", Permission.ResourceType.API, "/api/dashboard/system-status", Permission.HttpMethod.GET, dashboard.getId(), 2, 2, true);

        // 7. 认证相关权限
        Permission authManagement = createPermission("认证管理", "AUTH_MANAGEMENT", "认证管理模块", Permission.ResourceType.API, "/api/auth", Permission.HttpMethod.ALL, null, 1, 7, true);
        createPermission("用户登录", "AUTH_LOGIN", "用户登录", Permission.ResourceType.API, "/api/auth/login", Permission.HttpMethod.POST, authManagement.getId(), 2, 1, true);
        createPermission("用户注册", "AUTH_REGISTER", "用户注册", Permission.ResourceType.API, "/api/auth/register", Permission.HttpMethod.POST, authManagement.getId(), 2, 2, true);
        createPermission("用户登出", "AUTH_LOGOUT", "用户登出", Permission.ResourceType.API, "/api/auth/logout", Permission.HttpMethod.POST, authManagement.getId(), 2, 3, true);
        createPermission("获取用户信息", "AUTH_ME", "获取当前用户信息", Permission.ResourceType.API, "/api/auth/me", Permission.HttpMethod.GET, authManagement.getId(), 2, 4, true);
    }

    private Permission createPermission(String name, String code, String description, Permission.ResourceType resourceType, String resourcePath, Permission.HttpMethod httpMethod, Long parentId, Integer level, Integer sortOrder, Boolean isSystem) {
        Permission permission = new Permission();
        permission.setName(name);
        permission.setCode(code);
        permission.setDescription(description);
        permission.setResourceType(resourceType);
        permission.setResourcePath(resourcePath);
        permission.setHttpMethod(httpMethod);
        permission.setParentId(parentId);
        permission.setLevel(level);
        permission.setSortOrder(sortOrder);
        permission.setStatus(Permission.PermissionStatus.ACTIVE);
        permission.setIsSystem(isSystem);
        permission.setCreatedAt(java.time.LocalDateTime.now());
        permission.setUpdatedAt(java.time.LocalDateTime.now());
        permissionMapper.insertPermission(permission);
        return permission;
    }

    private void initRoles() {
        // 创建超级管理员角色
        Role superAdmin = new Role("超级管理员", "SUPER_ADMIN", "系统超级管理员，拥有所有权限");
        superAdmin.setIsSystem(true);
        superAdmin.setSortOrder(1);
        superAdmin.setCreatedAt(java.time.LocalDateTime.now());
        superAdmin.setUpdatedAt(java.time.LocalDateTime.now());
        roleMapper.insertRole(superAdmin);

        // 创建系统管理员角色
        Role systemAdmin = new Role("系统管理员", "SYSTEM_ADMIN", "系统管理员，负责系统配置和用户管理");
        systemAdmin.setIsSystem(true);
        systemAdmin.setSortOrder(2);
        systemAdmin.setCreatedAt(java.time.LocalDateTime.now());
        systemAdmin.setUpdatedAt(java.time.LocalDateTime.now());
        roleMapper.insertRole(systemAdmin);

        // 创建普通用户角色
        Role user = new Role("普通用户", "USER", "普通用户，基础权限");
        user.setIsSystem(true);
        user.setSortOrder(3);
        user.setCreatedAt(java.time.LocalDateTime.now());
        user.setUpdatedAt(java.time.LocalDateTime.now());
        roleMapper.insertRole(user);

        // 为超级管理员分配所有权限
        java.util.List<Permission> allPermissions = permissionMapper.findAll();
        java.util.List<Long> allPermissionIds = allPermissions.stream()
            .map(Permission::getId)
            .collect(java.util.stream.Collectors.toList());
        roleMapper.batchAssignPermissionsToRole(superAdmin.getId(), allPermissionIds);

        // 为系统管理员分配管理权限
        java.util.List<String> systemAdminCodes = Arrays.asList(
            "USER_MANAGEMENT", "USER_LIST", "USER_CREATE", "USER_UPDATE", "USER_RESET_PASSWORD", "USER_STATUS_MANAGE", "USER_ROLE_ASSIGN",
            "ROLE_MANAGEMENT", "ROLE_LIST", "ROLE_CREATE", "ROLE_UPDATE", "ROLE_ASSIGN_PERMISSIONS", "ROLE_USER_MANAGE",
            "PERMISSION_MANAGEMENT", "PERMISSION_LIST", "PERMISSION_TREE",
            "SYSTEM_MONITOR", "LOG_VIEW", "ONLINE_USERS", "HEALTH_CHECK",
            "DASHBOARD", "DASHBOARD_STATS", "DASHBOARD_SYSTEM_STATUS",
            "PROFILE", "PROFILE_VIEW", "PROFILE_UPDATE", "PASSWORD_CHANGE",
            "AUTH_MANAGEMENT", "AUTH_LOGIN", "AUTH_LOGOUT", "AUTH_ME"
        );
        java.util.List<Long> systemAdminPermissionIds = systemAdminCodes.stream()
            .map(code -> permissionMapper.findByCode(code))
            .filter(java.util.Objects::nonNull)
            .map(Permission::getId)
            .collect(java.util.stream.Collectors.toList());
        roleMapper.batchAssignPermissionsToRole(systemAdmin.getId(), systemAdminPermissionIds);

        // 为普通用户分配基础权限
        java.util.List<String> userCodes = Arrays.asList(
            "DASHBOARD", "DASHBOARD_STATS",
            "PROFILE", "PROFILE_VIEW", "PROFILE_UPDATE", "PASSWORD_CHANGE",
            "AUTH_MANAGEMENT", "AUTH_LOGIN", "AUTH_REGISTER", "AUTH_LOGOUT", "AUTH_ME"
        );
        java.util.List<Long> userPermissionIds = userCodes.stream()
            .map(code -> permissionMapper.findByCode(code))
            .filter(java.util.Objects::nonNull)
            .map(Permission::getId)
            .collect(java.util.stream.Collectors.toList());
        roleMapper.batchAssignPermissionsToRole(user.getId(), userPermissionIds);
    }

    private void initUsers() {
        // 创建默认超级管理员用户
        User admin = new User("admin", passwordEncoder.encode("admin123"), "<EMAIL>");
        admin.setRealName("系统管理员");
        admin.setStatus(User.UserStatus.ACTIVE);
        admin.setCreatedAt(java.time.LocalDateTime.now());
        admin.setUpdatedAt(java.time.LocalDateTime.now());
        userMapper.insertUser(admin);

        // 为admin用户分配超级管理员角色
        Role superAdminRole = roleMapper.findByCode("SUPER_ADMIN");
        if (superAdminRole == null) {
            throw new RuntimeException("超级管理员角色不存在");
        }
        roleMapper.assignRoleToUser(admin.getId(), superAdminRole.getId());

        log.info("已创建默认超级管理员用户: admin");
    }
}
