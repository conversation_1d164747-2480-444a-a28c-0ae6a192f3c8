package zh.backend.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zh.backend.entity.Permission;
import zh.backend.entity.UserPermission;

import java.util.List;
import java.util.Optional;

/**
 * 用户权限关联数据访问接口
 */
@Repository
public interface UserPermissionRepository extends JpaRepository<UserPermission, Long> {

    /**
     * 根据用户ID和权限ID查找用户权限关联
     */
    Optional<UserPermission> findByUserIdAndPermissionId(Long userId, Long permissionId);

    /**
     * 根据用户ID查找所有用户权限关联
     */
    List<UserPermission> findByUserId(Long userId);

    /**
     * 根据权限ID查找所有用户权限关联
     */
    List<UserPermission> findByPermissionId(Long permissionId);

    /**
     * 根据用户ID查找激活的用户权限关联
     */
    @Query("SELECT up FROM UserPermission up WHERE up.user.id = :userId AND up.status = 'ACTIVE'")
    List<UserPermission> findActiveByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找激活的权限
     */
    @Query("SELECT up.permission FROM UserPermission up WHERE up.user.id = :userId AND up.status = 'ACTIVE' AND up.permissionType = 'GRANT'")
    List<Permission> findActivePermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和权限类型查找用户权限关联
     */
    @Query("SELECT up FROM UserPermission up WHERE up.user.id = :userId AND up.permissionType = :permissionType AND up.status = 'ACTIVE'")
    List<UserPermission> findByUserIdAndPermissionType(@Param("userId") Long userId, @Param("permissionType") UserPermission.PermissionType permissionType);

    /**
     * 检查用户是否拥有指定权限
     */
    @Query("SELECT COUNT(up) > 0 FROM UserPermission up WHERE up.user.id = :userId AND up.permission.id = :permissionId AND up.status = 'ACTIVE' AND up.permissionType = 'GRANT'")
    boolean existsByUserIdAndPermissionId(@Param("userId") Long userId, @Param("permissionId") Long permissionId);

    /**
     * 根据用户ID删除所有用户权限关联
     */
    void deleteByUserId(Long userId);

    /**
     * 根据权限ID删除所有用户权限关联
     */
    void deleteByPermissionId(Long permissionId);

    /**
     * 查找即将过期的用户权限
     */
    @Query("SELECT up FROM UserPermission up WHERE up.expiresAt IS NOT NULL AND up.expiresAt <= CURRENT_TIMESTAMP AND up.status = 'ACTIVE'")
    List<UserPermission> findExpiredPermissions();

    /**
     * 统计用户的直接权限数量
     */
    @Query("SELECT COUNT(up) FROM UserPermission up WHERE up.user.id = :userId AND up.status = 'ACTIVE' AND up.permissionType = 'GRANT'")
    long countActivePermissionsByUserId(@Param("userId") Long userId);
}
